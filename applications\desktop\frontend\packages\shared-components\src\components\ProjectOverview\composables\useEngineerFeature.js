import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 工程特征业务逻辑组合式API
 */
export function useEngineerFeature() {
  // 状态管理
  const selectedRecord = ref(null)
  const clipboard = reactive({
    data: null,
    type: null
  })
  
  // 新增工程特征
  const addNewFeature = (parentRecord = null, featureName = '') => {
    const newRecord = {
      sequenceNbr: Date.now(), // 临时ID
      name: featureName || '新特征',
      remark: '',
      addFlag: 1, // 标记为新增
      lockFlag: 0,
      type: 'item',
      groupCode: parentRecord?.groupCode || 1,
      parentId: parentRecord?.sequenceNbr || null,
      childrenList: []
    }
    
    return newRecord
  }
  
  // 删除工程特征
  const deleteFeature = (record) => {
    if (!record) {
      message.warning('请选择要删除的特征')
      return false
    }
    
    if (!record.addFlag) {
      message.warning('只能删除新增的特征')
      return false
    }
    
    return true
  }
  
  // 复制工程特征
  const copyFeature = (record) => {
    if (!record) {
      message.warning('请选择要复制的特征')
      return
    }
    
    clipboard.data = {
      name: record.name,
      remark: record.remark,
      type: record.type,
      groupCode: record.groupCode
    }
    clipboard.type = 'feature'
    
    message.success('已复制到剪贴板')
  }
  
  // 粘贴工程特征
  const pasteFeature = (targetRecord = null) => {
    if (!clipboard.data || clipboard.type !== 'feature') {
      message.warning('剪贴板为空')
      return null
    }
    
    const newRecord = {
      ...clipboard.data,
      sequenceNbr: Date.now(),
      addFlag: 1,
      lockFlag: 0,
      parentId: targetRecord?.sequenceNbr || null,
      childrenList: []
    }
    
    return newRecord
  }
  
  // 检查是否可以粘贴
  const canPaste = computed(() => {
    return clipboard.data && clipboard.type === 'feature'
  })
  
  // 验证特征数据
  const validateFeature = (record) => {
    const errors = []
    
    if (!record.name || record.name.trim() === '') {
      errors.push('特征名称不能为空')
    }
    
    if (record.name && record.name.length > 50) {
      errors.push('特征名称长度不能超过50个字符')
    }
    
    if (record.remark && record.remark.length > 500) {
      errors.push('特征描述长度不能超过500个字符')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  // 预定义的工程特征模板
  const featureTemplates = {
    // 结构特征
    structure: [
      {
        name: '结构类型',
        options: ['框架结构', '剪力墙结构', '框剪结构', '砖混结构', '钢结构', '其他'],
        description: '建筑物的主要结构形式'
      },
      {
        name: '基础类型',
        options: ['独立基础', '条形基础', '筏板基础', '桩基础', '其他'],
        description: '建筑物的基础形式'
      },
      {
        name: '抗震设防烈度',
        options: ['6度', '7度', '8度', '9度'],
        description: '建筑物的抗震设防要求'
      }
    ],
    
    // 建筑特征
    architecture: [
      {
        name: '建筑层数',
        type: 'number',
        unit: '层',
        description: '建筑物的总层数'
      },
      {
        name: '建筑高度',
        type: 'number',
        unit: 'm',
        description: '建筑物的总高度'
      },
      {
        name: '层高',
        type: 'number',
        unit: 'm',
        description: '标准层的层高'
      }
    ],
    
    // 装修特征
    decoration: [
      {
        name: '装修标准',
        options: ['毛坯', '简装', '精装', '豪装'],
        description: '建筑物的装修标准'
      },
      {
        name: '外墙装修',
        options: ['涂料', '面砖', '石材', '玻璃幕墙', '金属幕墙'],
        description: '外墙的装修材料和做法'
      },
      {
        name: '屋面类型',
        options: ['平屋面', '坡屋面', '种植屋面', '其他'],
        description: '屋面的构造类型'
      }
    ],
    
    // 设备特征
    equipment: [
      {
        name: '电梯配置',
        description: '电梯的数量、类型和参数'
      },
      {
        name: '空调系统',
        options: ['中央空调', '分体空调', '多联机', '无空调'],
        description: '空调系统的类型和配置'
      },
      {
        name: '消防系统',
        description: '消防设施的配置情况'
      },
      {
        name: '智能化系统',
        description: '智能化设备和系统的配置'
      }
    ]
  }
  
  // 获取特征模板
  const getFeatureTemplate = (category) => {
    return featureTemplates[category] || []
  }
  
  // 获取所有特征模板
  const getAllFeatureTemplates = () => {
    return featureTemplates
  }
  
  // 根据模块类型获取推荐特征
  const getRecommendedFeatures = (moduleType) => {
    const baseFeatures = [
      ...featureTemplates.structure,
      ...featureTemplates.architecture
    ]
    
    switch (moduleType) {
      case 'estimate':
        // 概算阶段关注主要特征
        return [
          ...baseFeatures,
          ...featureTemplates.decoration.slice(0, 2)
        ]
        
      case 'budget':
        // 预算阶段需要详细特征
        return [
          ...baseFeatures,
          ...featureTemplates.decoration,
          ...featureTemplates.equipment
        ]
        
      case 'settlement':
        // 结算阶段关注实际情况
        return [
          ...baseFeatures,
          ...featureTemplates.decoration,
          {
            name: '变更情况',
            description: '施工过程中的设计变更情况'
          },
          {
            name: '签证情况', 
            description: '现场签证的具体情况'
          }
        ]
        
      case 'material':
        // 工料机关注材料和工艺
        return [
          ...baseFeatures.slice(0, 2),
          {
            name: '主要材料',
            description: '主要建筑材料的品牌和规格'
          },
          {
            name: '施工工艺',
            description: '主要施工工艺和方法'
          }
        ]
        
      default:
        return baseFeatures
    }
  }
  
  // 创建特征记录
  const createFeatureRecord = (template, parentRecord = null) => {
    return {
      sequenceNbr: Date.now(),
      name: template.name,
      remark: template.description || '',
      addFlag: 1,
      lockFlag: 0,
      type: 'item',
      groupCode: parentRecord?.groupCode || 1,
      parentId: parentRecord?.sequenceNbr || null,
      childrenList: [],
      // 扩展属性
      featureType: template.type || 'text',
      featureUnit: template.unit || '',
      featureOptions: template.options || []
    }
  }
  
  // 批量添加推荐特征
  const addRecommendedFeatures = (moduleType, parentRecord = null) => {
    const recommended = getRecommendedFeatures(moduleType)
    const records = recommended.map(template => 
      createFeatureRecord(template, parentRecord)
    )
    
    return records
  }
  
  // 搜索特征模板
  const searchFeatureTemplates = (keyword) => {
    const allTemplates = Object.values(featureTemplates).flat()
    
    if (!keyword || keyword.trim() === '') {
      return allTemplates
    }
    
    const lowerKeyword = keyword.toLowerCase()
    return allTemplates.filter(template => 
      template.name.toLowerCase().includes(lowerKeyword) ||
      (template.description && template.description.toLowerCase().includes(lowerKeyword))
    )
  }
  
  // 导出特征数据
  const exportFeatures = (features) => {
    const exportData = features.map(feature => ({
      特征名称: feature.name,
      特征描述: feature.remark || '',
      特征类型: feature.featureType || '文本',
      单位: feature.featureUnit || '',
      可选值: feature.featureOptions ? feature.featureOptions.join(', ') : ''
    }))
    
    return exportData
  }
  
  // 导入特征数据
  const importFeatures = (importData) => {
    return importData.map((item, index) => ({
      sequenceNbr: Date.now() + index,
      name: item.特征名称 || item.name || '',
      remark: item.特征描述 || item.description || item.remark || '',
      addFlag: 1,
      lockFlag: 0,
      type: 'item',
      groupCode: 1,
      parentId: null,
      childrenList: [],
      featureType: item.特征类型 || item.type || 'text',
      featureUnit: item.单位 || item.unit || '',
      featureOptions: item.可选值 ? item.可选值.split(',').map(s => s.trim()) : []
    }))
  }
  
  return {
    // 状态
    selectedRecord,
    clipboard,
    canPaste,
    
    // 特征操作
    addNewFeature,
    deleteFeature,
    copyFeature,
    pasteFeature,
    
    // 数据验证
    validateFeature,
    
    // 模板相关
    featureTemplates,
    getFeatureTemplate,
    getAllFeatureTemplates,
    getRecommendedFeatures,
    createFeatureRecord,
    addRecommendedFeatures,
    searchFeatureTemplates,
    
    // 数据导入导出
    exportFeatures,
    importFeatures
  }
}
