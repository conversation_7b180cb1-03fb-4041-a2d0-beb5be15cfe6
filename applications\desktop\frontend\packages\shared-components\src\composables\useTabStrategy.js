/**
 * Tab策略管理系统
 * 处理不同业务模块（概预结审）在同一Tab下的差异化逻辑
 */

// 导入各业务模块策略
import { BudgetStrategy } from './strategies/BudgetStrategy'
import { RoughEstimateStrategy } from './strategies/RoughEstimateStrategy'
import { SettlementStrategy } from './strategies/SettlementStrategy'
import { MaterialStrategy } from './strategies/MaterialStrategy'

// 策略注册表
const strategyRegistry = {
  'budget': BudgetStrategy,
  'rough-estimate': RoughEstimateStrategy,
  'settlement': SettlementStrategy,
  'material': MaterialStrategy
}

/**
 * Tab策略Hook
 */
export function useTabStrategy() {
  /**
   * 获取指定业务模块的Tab策略
   * @param {Object} params - 参数对象
   * @param {string} params.tabCode - Tab代码
   * @param {string} params.moduleType - 业务模块类型
   * @param {number} params.levelType - 层级类型
   * @param {boolean} params.isFirstLevelChild - 是否一级子单项
   * @param {boolean} params.isStandardGroup - 是否标准组模式
   * @param {boolean} params.hasProfessionalProject - 是否有专业工程
   * @returns {Object|null} 策略实例
   */
  const getTabStrategy = (params) => {
    const { moduleType } = params
    const StrategyClass = strategyRegistry[moduleType]

    if (!StrategyClass) {
      console.warn(`未找到模块 ${moduleType} 的策略实现`)
      return null
    }

    return new StrategyClass(params)
  }

  /**
   * 注册新的业务模块策略
   * @param {string} moduleType - 业务模块类型
   * @param {Function} StrategyClass - 策略类
   */
  const registerStrategy = (moduleType, StrategyClass) => {
    strategyRegistry[moduleType] = StrategyClass
  }

  /**
   * 获取所有可用的策略类型
   * @returns {Array<string>} 策略类型数组
   */
  const getAvailableStrategies = () => {
    return Object.keys(strategyRegistry)
  }

  return {
    getTabStrategy,
    registerStrategy,
    getAvailableStrategies
  }
}

/**
 * 基础Tab策略抽象类
 * 所有具体策略都应该继承此类
 */
export class BaseTabStrategy {
  constructor(params) {
    this.tabCode = params.tabCode
    this.moduleType = params.moduleType
    this.levelType = params.levelType
    this.isFirstLevelChild = params.isFirstLevelChild
    this.isStandardGroup = params.isStandardGroup
    this.hasProfessionalProject = params.hasProfessionalProject
    this.name = this.constructor.name
  }

  /**
   * 获取组件名称（必须由子类实现）
   * @param {Object} context - 上下文对象
   * @returns {string} 组件名称
   */
  getComponentName(context) {
    throw new Error('getComponentName方法必须由子类实现')
  }

  /**
   * 处理数据（可由子类重写）
   * @param {Object} data - 原始数据
   * @returns {Object} 处理后的数据
   */
  processData(data) {
    return {
      ...data,
      // 基础处理逻辑
      editable: this.isEditable(),
      permissions: this.getPermissions(),
      businessRules: this.getBusinessRules(),
      uiConfig: this.getUIConfig()
    }
  }

  /**
   * 处理数据变化（可由子类重写）
   * @param {Object} data - 变化的数据
   * @returns {Object} 处理后的数据
   */
  handleDataChange(data) {
    // 应用业务规则验证
    const validatedData = this.validateData(data)

    // 应用计算规则
    const calculatedData = this.applyCalculations(validatedData)

    return calculatedData
  }

  /**
   * 处理操作事件（可由子类重写）
   * @param {string} action - 操作类型
   * @param {Object} data - 操作数据
   * @returns {Object} 处理后的操作
   */
  handleAction(action, data) {
    // 检查操作权限
    if (!this.hasPermission(action)) {
      return {
        action: 'permission-denied',
        data: { originalAction: action, message: '无权限执行此操作' }
      }
    }

    // 应用业务逻辑处理
    return this.processAction(action, data)
  }

  /**
   * 检查是否可编辑（可由子类重写）
   * @returns {boolean} 是否可编辑
   */
  isEditable() {
    // 基础实现：默认可编辑
    return true
  }

  /**
   * 获取权限配置（可由子类重写）
   * @returns {Object} 权限配置
   */
  getPermissions() {
    return {
      canEdit: this.isEditable(),
      canDelete: this.isEditable(),
      canExport: true,
      canImport: this.isEditable()
    }
  }

  /**
   * 获取业务规则（可由子类重写）
   * @returns {Object} 业务规则配置
   */
  getBusinessRules() {
    return {
      validation: {},
      calculation: {},
      workflow: {}
    }
  }

  /**
   * 获取UI配置（可由子类重写）
   * @returns {Object} UI配置
   */
  getUIConfig() {
    return {
      showToolbar: true,
      showSummary: true,
      tableLayout: 'standard'
    }
  }

  /**
   * 数据验证（可由子类重写）
   * @param {Object} data - 要验证的数据
   * @returns {Object} 验证后的数据
   */
  validateData(data) {
    // 基础验证逻辑
    return data
  }

  /**
   * 应用计算规则（可由子类重写）
   * @param {Object} data - 要计算的数据
   * @returns {Object} 计算后的数据
   */
  applyCalculations(data) {
    // 基础计算逻辑
    return data
  }

  /**
   * 检查操作权限（可由子类重写）
   * @param {string} action - 操作类型
   * @returns {boolean} 是否有权限
   */
  hasPermission(action) {
    const permissions = this.getPermissions()
    const permissionMap = {
      'save': permissions.canEdit,
      'delete': permissions.canDelete,
      'export': permissions.canExport,
      'import': permissions.canImport
    }

    return permissionMap[action] !== false
  }

  /**
   * 处理具体操作（可由子类重写）
   * @param {string} action - 操作类型
   * @param {Object} data - 操作数据
   * @returns {Object} 处理后的操作
   */
  processAction(action, data) {
    return { action, data }
  }
}

/**
 * Tab策略工厂函数
 * @param {string} moduleType - 业务模块类型
 * @param {Object} params - 策略参数
 * @returns {Object|null} 策略实例
 */
export function createTabStrategy(moduleType, params) {
  const { getTabStrategy } = useTabStrategy()
  return getTabStrategy({ ...params, moduleType })
}