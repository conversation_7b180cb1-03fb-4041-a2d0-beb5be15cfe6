<template>
  <div class="tab-menu-example">
    <h1>TabMenu 组件使用示例</h1>

    <div class="example-section">
      <h2>控制面板</h2>
      <a-form layout="inline" style="margin-bottom: 16px;">
        <a-form-item label="模块类型">
          <a-select v-model:value="currentModule" style="width: 150px;">
            <a-select-option value="budget">预算模块</a-select-option>
            <a-select-option value="rough-estimate">概算模块</a-select-option>
            <a-select-option value="settlement">结算模块</a-select-option>
            <a-select-option value="material">工料机模块</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="层级类型">
          <a-select v-model:value="currentLevel" style="width: 150px;">
            <a-select-option :value="1">工程项目层级</a-select-option>
            <a-select-option :value="2">单项工程层级</a-select-option>
            <a-select-option :value="3">单位工程层级</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="是否一级子单项">
          <a-switch v-model:checked="isFirstLevel" />
        </a-form-item>

        <a-form-item label="标准组模式">
          <a-switch v-model:checked="isStandardGroup" />
        </a-form-item>

        <a-form-item label="有专业工程">
          <a-switch v-model:checked="hasProfessional" />
        </a-form-item>
      </a-form>
    </div>

    <div class="example-section">
      <h2>TabMenu 效果</h2>
      <div class="tab-demo-container">
        <TabMenu
          ref="tabMenuRef"
          :level-type="currentLevel"
          :module-type="currentModule"
          :is-first-level-child="isFirstLevel"
          :is-standard-group="isStandardGroup"
          :has-professional-project="hasProfessional"
          :class="`${currentModule}-theme`"
          @tab-change="handleTabChange"
          @get-title="handleTitleChange"
        />
      </div>
    </div>

    <div class="example-section">
      <h2>当前状态信息</h2>
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="当前模块">{{ moduleNames[currentModule] }}</a-descriptions-item>
        <a-descriptions-item label="当前层级">{{ levelNames[currentLevel] }}</a-descriptions-item>
        <a-descriptions-item label="活跃Tab">{{ currentTabInfo.value || '无' }}</a-descriptions-item>
        <a-descriptions-item label="Tab代码">{{ currentTabInfo.code || '无' }}</a-descriptions-item>
        <a-descriptions-item label="可用Tab数量">{{ availableTabsCount }}</a-descriptions-item>
        <a-descriptions-item label="一级子单项">{{ isFirstLevel ? '是' : '否' }}</a-descriptions-item>
        <a-descriptions-item label="标准组模式">{{ isStandardGroup ? '是' : '否' }}</a-descriptions-item>
        <a-descriptions-item label="有专业工程">{{ hasProfessional ? '是' : '否' }}</a-descriptions-item>
      </a-descriptions>
    </div>

    <div class="example-section">
      <h2>可用Tab列表</h2>
      <a-table
        :columns="tabColumns"
        :data-source="availableTabs"
        :pagination="false"
        size="small"
      />
    </div>

    <div class="example-section">
      <h2>外部调用方法测试</h2>
      <a-space>
        <a-button @click="testChangeByName('工程概况')">切换到工程概况</a-button>
        <a-button @click="testChangeByName('分部分项')">切换到分部分项</a-button>
        <a-button @click="testChangeByName('人材机汇总')">切换到人材机汇总</a-button>
        <a-button @click="getCurrentTabInfo">获取当前Tab信息</a-button>
      </a-space>
    </div>

    <div class="example-section">
      <h2>事件日志</h2>
      <div class="event-log">
        <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-event">{{ log.event }}</span>
          <span class="log-data">{{ log.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import TabMenu from './TabMenu.vue'

// 控制变量
const currentModule = ref('budget')
const currentLevel = ref(3)
const isFirstLevel = ref(true)
const isStandardGroup = ref(false)
const hasProfessional = ref(true)

// Tab组件引用
const tabMenuRef = ref(null)

// 当前Tab信息
const currentTabInfo = ref({})

// 事件日志
const eventLogs = ref([])

// 映射表
const moduleNames = {
  'budget': '预算模块',
  'rough-estimate': '概算模块',
  'settlement': '结算模块',
  'material': '工料机模块'
}

const levelNames = {
  1: '工程项目层级',
  2: '单项工程层级',
  3: '单位工程层级'
}

// 计算属性
const availableTabs = computed(() => {
  return tabMenuRef.value?.filteredTabs || []
})

const availableTabsCount = computed(() => {
  return availableTabs.value.length
})

// 表格列配置
const tabColumns = [
  {
    title: 'Tab代码',
    dataIndex: 'code',
    key: 'code',
    width: 100
  },
  {
    title: 'Tab名称',
    dataIndex: 'value',
    key: 'value'
  }
]

// 事件处理
const handleTabChange = (tabData) => {
  currentTabInfo.value = tabData
  addEventLog('Tab切换', `${tabData.value} (${tabData.code})`)
}

const handleTitleChange = (title) => {
  addEventLog('标题变更', title)
}

// 添加事件日志
const addEventLog = (event, data) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`

  eventLogs.value.unshift({
    time,
    event,
    data: typeof data === 'object' ? JSON.stringify(data) : data
  })

  // 只保留最近20条日志
  if (eventLogs.value.length > 20) {
    eventLogs.value = eventLogs.value.slice(0, 20)
  }
}

// 外部方法测试
const testChangeByName = (tabName) => {
  if (tabMenuRef.value) {
    const success = tabMenuRef.value.changeTabByName(tabName)
    addEventLog('外部调用切换', `切换到 ${tabName} ${success ? '成功' : '失败'}`)
  }
}

const getCurrentTabInfo = () => {
  if (tabMenuRef.value) {
    const info = tabMenuRef.value.getCurrentTab()
    addEventLog('获取当前Tab', info ? `${info.value} (${info.code})` : '无')
  }
}

// 监听配置变化
watch([currentModule, currentLevel, isFirstLevel, isStandardGroup, hasProfessional], () => {
  addEventLog('配置变更', `${moduleNames[currentModule.value]} - ${levelNames[currentLevel.value]}`)
})
</script>

<style lang="scss" scoped>
.tab-menu-example {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .example-section {
    margin-bottom: 32px;

    h2 {
      margin-bottom: 16px;
      color: #1890ff;
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 8px;
    }
  }

  .tab-demo-container {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    background: #fafafa;
  }

  .event-log {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 12px;
    background: #f8f8f8;

    .log-item {
      display: flex;
      margin-bottom: 4px;
      font-size: 12px;
      font-family: 'Courier New', monospace;

      .log-time {
        color: #666;
        margin-right: 12px;
        width: 60px;
      }

      .log-event {
        color: #1890ff;
        margin-right: 12px;
        width: 80px;
        font-weight: 500;
      }

      .log-data {
        color: #333;
        flex: 1;
      }
    }
  }
}

// 模块主题样式
:deep(.budget-theme .ant-tabs-tab-active) {
  background-color: #f6ffed;
  border-bottom: 2px solid #52c41a;
  .ant-tabs-tab-btn { color: #52c41a; }
}

:deep(.rough-estimate-theme .ant-tabs-tab-active) {
  background-color: #e6f7ff;
  border-bottom: 2px solid #1890ff;
  .ant-tabs-tab-btn { color: #1890ff; }
}

:deep(.settlement-theme .ant-tabs-tab-active) {
  background-color: #f9f0ff;
  border-bottom: 2px solid #722ed1;
  .ant-tabs-tab-btn { color: #722ed1; }
}

:deep(.material-theme .ant-tabs-tab-active) {
  background-color: #fff7e6;
  border-bottom: 2px solid #faad14;
  .ant-tabs-tab-btn { color: #faad14; }
}
</style>