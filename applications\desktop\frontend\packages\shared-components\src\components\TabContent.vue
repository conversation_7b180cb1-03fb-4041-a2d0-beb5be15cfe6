<template>
  <div class="tab-content-wrapper">
    <component
      :is="currentComponent"
      v-if="currentComponent"
      :module-type="moduleType"
      :level-type="levelType"
      :tab-info="currentTab"
      :project-data="projectData"
      :table-data="tableData"
      :is-first-level-child="isFirstLevelChild"
      :is-standard-group="isStandardGroup"
      :has-professional-project="hasProfessionalProject"
      v-bind="componentProps"
      @data-change="handleDataChange"
      @action="handleAction"
    />
    <div v-else class="no-content">
      <a-empty
        :description="`${currentTab?.value || '当前Tab'} 页面开发中...`"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineAsyncComponent } from 'vue'
import { Empty } from 'ant-design-vue'

const props = defineProps({
  // 当前活跃的Tab信息
  currentTab: {
    type: Object,
    default: () => ({})
  },
  // 模块类型
  moduleType: {
    type: String,
    required: true,
    validator: (value) => ['budget', 'rough-estimate', 'settlement', 'material'].includes(value)
  },
  // 层级类型
  levelType: {
    type: Number,
    required: true,
    validator: (value) => [1, 2, 3].includes(value)
  },
  // 项目数据
  projectData: {
    type: Object,
    default: () => ({})
  },
  // 表格数据
  tableData: {
    type: Array,
    default: () => []
  },
  // 权限配置
  isFirstLevelChild: {
    type: Boolean,
    default: true
  },
  isStandardGroup: {
    type: Boolean,
    default: false
  },
  hasProfessionalProject: {
    type: Boolean,
    default: true
  },
  // 额外的组件属性
  componentProps: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['dataChange', 'action', 'componentLoad'])

// 定义Tab Code到组件的映射关系
const componentMapping = {
  // 通用映射 - 基于Tab code
  '1': {
    // 根据层级和模块类型确定具体组件
    component: (levelType, moduleType) => {
      if (levelType === 1) return 'ProjectOverview' // 工程项目：项目概况
      if (levelType === 3 && moduleType === 'settlement') return 'ProjectOverview' // 结算单位工程：项目概况
      return 'ProjectOverview' // 默认：工程概况
    }
  },
  '2': {
    component: () => 'CostAnalysis' // 造价分析
  },
  '3': {
    component: () => 'FeeTable' // 取费表
  },
  '4': {
    component: (levelType, moduleType) => {
      if (levelType === 1 || levelType === 2) return 'MaterialSummary' // 项目/单项：人材机汇总
      return 'ItemDetail' // 单位工程：分部分项
    }
  },
  '5': {
    component: (levelType, moduleType) => {
      if (levelType === 1) return 'EquipmentPurchase' // 工程项目：设备购置费
      return 'MeasureProject' // 单位工程：措施项目
    }
  },
  '6': {
    component: (levelType, moduleType) => {
      if (levelType === 1) return 'ConstructionOtherFee' // 工程项目：建设其他费
      return 'MaterialSummary' // 单位工程：人材机汇总
    }
  },
  '7': {
    component: (levelType, moduleType) => {
      if (levelType === 1) return 'EstimateSummary' // 工程项目：概算汇总
      return 'OtherProject' // 单位工程：其他项目
    }
  },
  '8': {
    component: (levelType, moduleType) => {
      if (levelType === 1) return 'AdjustEstimate' // 工程项目：调整概算
      return 'CostSummary' // 单位工程：费用汇总
    }
  },
  '9': {
    component: () => 'IndependentFee' // 独立费
  }
}

// 异步组件加载器
const createAsyncComponent = (componentName) => {
  return defineAsyncComponent({
    loader: () => {
      try {
        return import(`./tab-pages/${componentName}.vue`)
      } catch (error) {
        console.warn(`组件 ${componentName} 未找到，将显示占位内容`)
        return Promise.resolve({
          template: `
            <div class="component-placeholder">
              <a-result
                status="info"
                :title="'${componentName} 组件'"
                sub-title="该功能页面正在开发中..."
              >
                <template #icon>
                  <a-icon type="experiment" />
                </template>
              </a-result>
            </div>
          `
        })
      }
    },
    loadingComponent: {
      template: `
        <div class="component-loading">
          <a-spin size="large" tip="加载中...">
            <div style="height: 200px;"></div>
          </a-spin>
        </div>
      `
    },
    errorComponent: {
      template: `
        <div class="component-error">
          <a-result
            status="error"
            title="组件加载失败"
            sub-title="请检查网络连接或刷新页面重试"
          >
            <template #extra>
              <a-button type="primary" @click="$emit('reload')">重新加载</a-button>
            </template>
          </a-result>
        </div>
      `
    },
    delay: 200,
    timeout: 10000
  })
}

// 计算当前应该显示的组件
const currentComponent = computed(() => {
  const tabCode = props.currentTab?.code
  if (!tabCode || !componentMapping[tabCode]) {
    return null
  }

  const mapping = componentMapping[tabCode]
  let componentName

  if (typeof mapping.component === 'function') {
    componentName = mapping.component(props.levelType, props.moduleType)
  } else {
    componentName = mapping.component
  }

  return createAsyncComponent(componentName)
})

// 监听Tab切换，触发组件加载事件
watch(() => props.currentTab, (newTab) => {
  if (newTab?.code) {
    emit('componentLoad', {
      tabCode: newTab.code,
      tabName: newTab.value,
      componentName: getComponentName(newTab.code)
    })
  }
}, { immediate: true })

// 获取组件名称（用于调试和日志）
const getComponentName = (tabCode) => {
  const mapping = componentMapping[tabCode]
  if (!mapping) return null

  if (typeof mapping.component === 'function') {
    return mapping.component(props.levelType, props.moduleType)
  }
  return mapping.component
}

// 处理子组件的数据变化
const handleDataChange = (data) => {
  emit('dataChange', {
    tabCode: props.currentTab?.code,
    tabName: props.currentTab?.value,
    data
  })
}

// 处理子组件的操作事件
const handleAction = (action, data) => {
  emit('action', {
    tabCode: props.currentTab?.code,
    tabName: props.currentTab?.value,
    action,
    data
  })
}
</script>

<style lang="scss" scoped>
.tab-content-wrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;

  .no-content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;
  }

  .component-loading {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .component-placeholder,
  .component-error {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// 子组件样式穿透
:deep(.tab-page-content) {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>