<!--
  CostTable 组件测试页面
  验证新的模块化CostTable组件是否正常工作
-->
<template>
  <div class="cost-table-test">
    <AppHeader
      title="CostTable 组件测试"
      :show-window-controls="true"
    />

    <div class="test-content">
      <!-- 模块切换器 -->
      <div class="module-switcher">
        <h3>模块类型测试</h3>
        <a-radio-group v-model:value="currentModule" @change="handleModuleChange">
          <a-radio-button value="gaiSuan">概算</a-radio-button>
          <a-radio-button value="yuSuan">预算</a-radio-button>
          <a-radio-button value="jieSuan">结算</a-radio-button>
          <a-radio-button value="shenHe">审核</a-radio-button>
          <a-radio-button value="gongLiaoJi">工料机</a-radio-button>
        </a-radio-group>
      </div>

      <!-- 操作工具栏 -->
      <div class="toolbar">
        <a-space>
          <a-button @click="loadTestData">加载测试数据</a-button>
          <a-button @click="clearData">清空数据</a-button>
          <a-button @click="exportData">导出数据</a-button>
        </a-space>
      </div>

      <!-- CostTable 组件 -->
      <div class="table-container">
        <CostTable
          ref="costTableRef"
          :data="tableData"
          :module-type="currentModule"
          :editable="true"
          :range-selection="true"
          :bordered="true"
          table-size="small"
          :scroll-config="{ x: 1200, y: 'calc(100vh - 300px)' }"
          @data-change="handleDataChange"
          @row-select="handleRowSelect"
          @cell-edit="handleCellEdit"
          @cell-edited="handleCellEdited"
          @add-row="handleAddRow"
          @delete-row="handleDeleteRow"
          @copy-rows="handleCopyRows"
          @paste-rows="handlePasteRows"
          @component-action="handleComponentAction"
          @record-update="handleRecordUpdate"
        />
      </div>

      <!-- 事件日志 -->
      <div class="event-log">
        <h4>事件日志</h4>
        <div class="log-content">
          <div
            v-for="(log, index) in eventLogs"
            :key="index"
            class="log-item"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-event">{{ log.event }}</span>
            <span class="log-detail">{{ log.detail }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { CostTable } from '@cost-app/shared-components'
import AppHeader from '../layouts/AppHeader.vue'

// 响应式数据
const costTableRef = ref(null)
const currentModule = ref('yuSuan')
const tableData = ref([])
const eventLogs = ref([])

// 生成测试数据
const generateTestData = (moduleType) => {
  const isEstimate = moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi'
  const codeField = isEstimate ? 'deCode' : 'bdCode'
  const nameField = isEstimate ? 'deName' : 'name'

  return [
    {
      id: 1,
      kind: '01',
      [codeField]: '01',
      [nameField]: '建筑工程',
      level: 0,
      children: [
        {
          id: 2,
          kind: '02',
          [codeField]: '0101',
          [nameField]: '土建工程',
          level: 1,
          children: [
            {
              id: 3,
              kind: isEstimate ? '-1' : '03',
              [codeField]: isEstimate ? 'A1-1' : '010101001',
              [nameField]: '挖土方',
              unit: 'm³',
              quantity: 100,
              price: 25.5,
              amount: 2550,
              level: 2,
              projectAttr: moduleType !== 'gaiSuan' && moduleType !== 'gongLiaoJi' ? '机械挖土' : undefined,
              specification: '三类土'
            }
          ]
        }
      ]
    },
    {
      id: 4,
      kind: '01',
      [codeField]: '02',
      [nameField]: '装饰工程',
      level: 0,
      children: [
        {
          id: 5,
          kind: '02',
          [codeField]: '0201',
          [nameField]: '墙面装饰',
          level: 1,
          children: [
            {
              id: 6,
              kind: isEstimate ? '-1' : '03',
              [codeField]: isEstimate ? 'A2-1' : '020101001',
              [nameField]: '内墙涂料',
              unit: 'm²',
              quantity: 500,
              price: 15.8,
              amount: 7900,
              level: 2,
              projectAttr: moduleType !== 'gaiSuan' && moduleType !== 'gongLiaoJi' ? '乳胶漆' : undefined,
              specification: '两遍成活'
            }
          ]
        }
      ]
    }
  ]
}

// 添加事件日志
const addEventLog = (event, detail) => {
  const now = new Date()
  const time = now.toLocaleTimeString()
  
  eventLogs.value.unshift({
    time,
    event,
    detail
  })
  
  // 保持最多20条日志
  if (eventLogs.value.length > 20) {
    eventLogs.value = eventLogs.value.slice(0, 20)
  }
}

// 事件处理方法
const handleModuleChange = () => {
  addEventLog('模块切换', `切换到${currentModule.value}模块`)
  loadTestData()
}

const loadTestData = () => {
  tableData.value = generateTestData(currentModule.value)
  addEventLog('数据加载', `加载${currentModule.value}模块测试数据`)
  message.success('测试数据加载成功')
}

const clearData = () => {
  tableData.value = []
  addEventLog('数据清空', '清空所有数据')
  message.success('数据已清空')
}

const exportData = () => {
  console.log('导出数据:', tableData.value)
  addEventLog('数据导出', `导出${tableData.value.length}条记录`)
  message.success('数据导出成功（查看控制台）')
}

const handleDataChange = (data) => {
  addEventLog('数据变更', `数据发生变更，共${data.length}条记录`)
}

const handleRowSelect = (selectedRows) => {
  addEventLog('行选择', `选中${selectedRows.length}行`)
}

const handleCellEdit = (editData) => {
  addEventLog('单元格编辑', `开始编辑字段: ${editData.field}`)
}

const handleCellEdited = (editData) => {
  addEventLog('单元格编辑完成', `${editData.field}: ${editData.oldValue} → ${editData.newValue}`)
}

const handleAddRow = (addData) => {
  addEventLog('新增行', `新增${addData.type || '行'}`)
}

const handleDeleteRow = (record) => {
  const nameField = currentModule.value === 'gaiSuan' || currentModule.value === 'gongLiaoJi' ? 'deName' : 'name'
  addEventLog('删除行', `删除: ${record[nameField]}`)
}

const handleCopyRows = (rows) => {
  addEventLog('复制行', `复制${rows.length}行`)
}

const handlePasteRows = (pasteData) => {
  addEventLog('粘贴行', `粘贴${pasteData.rows.length}行`)
}

const handleComponentAction = (actionData) => {
  addEventLog('组件操作', `执行操作: ${actionData.type}`)
  message.info(`执行了${actionData.type}操作`)
}

const handleRecordUpdate = (updateData) => {
  addEventLog('记录更新', `更新字段: ${updateData.field}`)
}

// 初始化
onMounted(() => {
  loadTestData()
  addEventLog('页面初始化', '测试页面加载完成')
})
</script>

<style scoped>
.cost-table-test {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.test-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  gap: 16px;
}

.module-switcher {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 6px;
}

.module-switcher h3 {
  margin: 0 0 12px 0;
  color: #1890ff;
}

.toolbar {
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.table-container {
  flex: 1;
  min-height: 0;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
}

.event-log {
  height: 200px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 12px;
}

.event-log h4 {
  margin: 0 0 12px 0;
  color: #1890ff;
}

.log-content {
  height: 150px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  padding: 2px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-time {
  color: #999;
  width: 80px;
  flex-shrink: 0;
}

.log-event {
  color: #1890ff;
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
}

.log-detail {
  color: #666;
  flex: 1;
}
</style>
