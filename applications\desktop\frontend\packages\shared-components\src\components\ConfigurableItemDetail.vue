<template>
  <div class="configurable-item-detail">
    <CostTable
      :data="processedTableData"
      :columns="computedColumns"
      :module-type="moduleType"
      :editable="editable"
      :show-toolbar="showToolbar"
      :show-summary="showSummary"
      :range-selection="rangeSelection"
      :tree-props="treeProps"
      :scroll-config="scrollConfig"
      :table-type="tableType"
      :row-key="rowKey"
      :default-expand-all="defaultExpandAll"
      @data-change="handleDataChange"
      @cell-edited="handleCellEdited"
      @row-select="handleRowSelect"
      @add-row="handleAddRow"
      @delete-row="handleDeleteRow"
      @context-menu="handleContextMenuClick"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { CostTable } from '../index.js'
import { 
  MODULE_FIELD_CONFIGS, 
  generateFieldEditConfig,
  getModuleConfig 
} from './CostTable/moduleConfigs.js'

const props = defineProps({
  // 模块类型：gaiSuan, yuSuan, jieSuan, shenHe
  moduleType: {
    type: String,
    required: true,
    validator: (value) => ['gaiSuan', 'yuSuan', 'jieSuan', 'shenHe'].includes(value)
  },
  
  // 表格数据
  tableData: {
    type: Array,
    default: () => []
  },
  
  // 自定义字段配置
  customFieldConfig: {
    type: Object,
    default: () => ({})
  },
  
  // 自定义列配置
  customColumns: {
    type: Array,
    default: () => []
  },
  
  // 编辑条件配置
  editConditions: {
    type: Object,
    default: () => ({})
  },
  
  // 表格配置
  editable: {
    type: Boolean,
    default: true
  },
  showToolbar: {
    type: Boolean,
    default: true
  },
  showSummary: {
    type: Boolean,
    default: true
  },
  rangeSelection: {
    type: Boolean,
    default: true
  },
  tableType: {
    type: String,
    default: 'budget'
  },
  rowKey: {
    type: String,
    default: 'id'
  },
  defaultExpandAll: {
    type: Boolean,
    default: true
  },
  scrollConfig: {
    type: Object,
    default: () => ({ x: 1600, y: 'calc(100vh - 200px)' })
  },
  
  // 项目信息
  projectData: {
    type: Object,
    default: () => ({})
  },
  
  // 标签信息
  tabInfo: {
    type: Object,
    default: () => ({})
  },
  
  // 层级类型
  levelType: {
    type: Number,
    default: 1
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 响应式数据
const processedTableData = ref([])
const moduleConfig = computed(() => getModuleConfig(props.moduleType))

// 树形配置
const treeProps = {
  children: 'children',
  hasChildren: 'hasChildren'
}

// 计算列配置
const computedColumns = computed(() => {
  // 如果提供了自定义列配置，直接使用
  if (props.customColumns && props.customColumns.length > 0) {
    return enhanceColumnsWithEditConfig(props.customColumns)
  }
  
  // 否则使用模块默认配置
  const moduleFieldConfig = MODULE_FIELD_CONFIGS[props.moduleType]
  if (!moduleFieldConfig) {
    console.warn(`未找到模块 ${props.moduleType} 的字段配置`)
    return []
  }
  
  const baseColumns = moduleFieldConfig.fields.map(field => ({
    title: field.title,
    field: field.field,
    dataIndex: field.field,
    width: field.width,
    align: getFieldAlign(field),
    fixed: field.fixed,
    editable: field.editable !== false,
    required: field.required || false,
    dataType: field.dataType || 'string',
    formatter: getFieldFormatter(field),
    hidden: field.hidden || false,
    component: field.component || 'input',
    componentProps: field.componentProps || {},
    options: field.options || [],
    editableCondition: field.editableCondition
  }))
  
  return enhanceColumnsWithEditConfig(baseColumns)
})

// 增强列配置，添加编辑控制
const enhanceColumnsWithEditConfig = (columns) => {
  return columns.map(column => {
    // 应用自定义字段配置
    const customConfig = props.customFieldConfig[column.field] || {}
    
    // 合并配置
    const enhancedColumn = {
      ...column,
      ...customConfig
    }
    
    // 如果有编辑条件，设置动态编辑函数
    if (enhancedColumn.editableCondition) {
      enhancedColumn.editable = (context) => {
        return checkEditCondition(enhancedColumn.editableCondition, context)
      }
    }
    
    // 应用编辑条件配置
    if (props.editConditions[column.field]) {
      enhancedColumn.editable = (context) => {
        return props.editConditions[column.field](context)
      }
    }
    
    return enhancedColumn
  })
}

// 检查编辑条件
const checkEditCondition = (condition, context) => {
  const { record } = context
  
  // 根据不同条件类型进行判断
  switch (condition) {
    case 'contractType':
      // 结算模块中，只有合同外和变更项目的工程量可编辑
      return record.contractType === 'external' || record.contractType === 'change'
    
    case 'auditMode':
      // 审核模块中的特殊编辑条件
      return record.auditStatus !== 'locked'
    
    default:
      return true
  }
}

// 获取字段对齐方式
const getFieldAlign = (field) => {
  if (field.dataType === 'number') return 'right'
  if (field.field === 'unit') return 'center'
  return 'left'
}

// 获取字段格式化器
const getFieldFormatter = (field) => {
  if (field.dataType === 'number') {
    const precision = field.field.includes('Price') || field.field.includes('Cost') || field.field.includes('Amount') ? 2 : 3
    return (value) => Number(value || 0).toFixed(precision)
  }
  return null
}

// 数据处理
const processTableData = () => {
  console.log('ConfigurableItemDetail - processTableData called')
  console.log('ConfigurableItemDetail - props.tableData:', props.tableData)
  console.log('ConfigurableItemDetail - moduleType:', props.moduleType)

  if (!props.tableData || props.tableData.length === 0) {
    console.log('ConfigurableItemDetail - 没有数据，使用默认数据')
    // 如果没有数据，使用模块默认测试数据
    processedTableData.value = getDefaultDataForModule()
    console.log('ConfigurableItemDetail - 默认数据:', processedTableData.value)
    return
  }
  
  // 处理数据，添加模块特有字段
  processedTableData.value = props.tableData.map(item => {
    const processedItem = { ...item }
    
    // 根据模块类型添加特有字段
    switch (props.moduleType) {
      case 'yuSuan':
        processedItem.budgetUnitPrice = processedItem.budgetUnitPrice || processedItem.unitPrice || 0
        processedItem.budgetAmount = processedItem.budgetAmount || processedItem.amount || 0
        processedItem.quotaCode = processedItem.quotaCode || ''
        processedItem.quotaUnitPrice = processedItem.quotaUnitPrice || 0
        break
        
      case 'jieSuan':
        processedItem.contractQuantity = processedItem.contractQuantity || processedItem.quantity || 0
        processedItem.contractUnitPrice = processedItem.contractUnitPrice || processedItem.unitPrice || 0
        processedItem.settlementUnitPrice = processedItem.settlementUnitPrice || processedItem.unitPrice || 0
        processedItem.settlementAmount = processedItem.settlementAmount || processedItem.amount || 0
        processedItem.contractType = processedItem.contractType || 'within'
        break
        
      case 'shenHe':
        processedItem.auditQuantity = processedItem.auditQuantity || processedItem.quantity || 0
        processedItem.auditUnitPrice = processedItem.auditUnitPrice || processedItem.unitPrice || 0
        processedItem.auditAmount = processedItem.auditAmount || processedItem.amount || 0
        processedItem.auditOpinion = processedItem.auditOpinion || ''
        processedItem.auditStatus = processedItem.auditStatus || 'pending'
        break
    }
    
    return processedItem
  })
}

// 获取模块默认数据
const getDefaultDataForModule = () => {
  // 这里可以根据不同模块返回不同的默认数据
  // 为了简化，返回空数组，实际使用中可以从配置文件加载
  return []
}

// 事件处理
const handleDataChange = (newData) => {
  processedTableData.value = newData
  emit('dataChange', newData)
}

const handleCellEdited = ({ record, column, newValue, oldValue }) => {
  console.log(`${props.moduleType} 单元格编辑:`, { record, column, newValue, oldValue })
  
  // 根据模块类型执行特定的计算逻辑
  performModuleSpecificCalculation(record, column, newValue)
  
  emit('action', 'cellEdited', { record, column, newValue, oldValue })
  message.success(`${moduleConfig.value.name}数据更新成功`)
}

// 模块特定计算逻辑
const performModuleSpecificCalculation = (record, column, newValue) => {
  switch (props.moduleType) {
    case 'yuSuan':
      if (column.field === 'quantity' || column.field === 'budgetUnitPrice') {
        record.budgetAmount = (record.quantity || 0) * (record.budgetUnitPrice || 0)
      }
      break
      
    case 'jieSuan':
      if (column.field === 'quantity' || column.field === 'settlementUnitPrice') {
        record.settlementAmount = (record.quantity || 0) * (record.settlementUnitPrice || 0)
      }
      break
      
    case 'shenHe':
      if (column.field === 'auditQuantity' || column.field === 'auditUnitPrice') {
        record.auditAmount = (record.auditQuantity || 0) * (record.auditUnitPrice || 0)
      }
      break
  }
}

const handleRowSelect = (selectedKeys, selectedRows) => {
  emit('action', 'rowSelect', { selectedKeys, selectedRows })
}

const handleAddRow = (newRow) => {
  emit('action', 'addRow', { ...newRow, type: props.moduleType })
}

const handleDeleteRow = (deletedRows) => {
  emit('action', 'deleteRow', deletedRows)
}

const handleContextMenuClick = ({ key }, record) => {
  console.log(`${props.moduleType} 右键菜单操作:`, { key, record })
  emit('action', 'contextMenu', { key, record })
}

// 监听数据变化
watch(() => props.tableData, () => {
  processTableData()
}, { deep: true })

onMounted(() => {
  processTableData()
})

// 暴露方法
defineExpose({
  getTableData: () => processedTableData.value,
  refreshData: () => processTableData()
})
</script>

<style lang="scss" scoped>
.configurable-item-detail {
  height: 100%;
  overflow: hidden;
}
</style>
