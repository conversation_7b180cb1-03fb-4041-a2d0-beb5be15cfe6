<!--
  占位符组件
  当模块特有组件加载失败时显示
-->
<template>
  <a-modal
    v-model:open="visible"
    title="功能暂未实现"
    width="400px"
    :footer="null"
    @cancel="handleCancel"
  >
    <div class="placeholder-content">
      <a-result
        status="info"
        title="功能开发中"
        sub-title="该功能正在开发中，敬请期待"
      >
        <template #icon>
          <ExclamationCircleOutlined style="color: #1890ff" />
        </template>
        <template #extra>
          <a-button type="primary" @click="handleCancel">
            确定
          </a-button>
        </template>
      </a-result>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'cancel'])

// 响应式数据
const visible = ref(props.visible)

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
const handleCancel = () => {
  visible.value = false
  emit('cancel')
}
</script>

<style scoped>
.placeholder-content {
  text-align: center;
  padding: 20px;
}
</style>
