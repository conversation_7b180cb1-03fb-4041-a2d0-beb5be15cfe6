<template>
  <div class="budget-project-overview">
    <div class="page-header">
      <h3>预算项目概况</h3>
      <div class="page-actions">
        <a-button type="primary" @click="handleSave">
          <template #icon><SaveOutlined /></template>
          保存预算概况
        </a-button>
        <a-button @click="handleExport">
          <template #icon><ExportOutlined /></template>
          导出预算报告
        </a-button>
      </div>
    </div>

    <div class="page-body">
      <!-- 预算基本信息 -->
      <a-card title="预算基本信息" class="info-card">
        <a-form
          :model="budgetInfo"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
        >
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="预算名称">
                <a-input
                  v-model:value="budgetInfo.name"
                  placeholder="请输入预算名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="预算编号">
                <a-input
                  v-model:value="budgetInfo.code"
                  placeholder="请输入预算编号"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="预算总金额">
                <a-input-number
                  v-model:value="budgetInfo.totalAmount"
                  :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                  :parser="value => value.replace(/¥\s?|(,*)/g, '')"
                  style="width: 100%"
                  placeholder="请输入预算总金额"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="编制日期">
                <a-date-picker
                  v-model:value="budgetInfo.compileDate"
                  style="width: 100%"
                  placeholder="请选择编制日期"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="建设单位">
                <a-input
                  v-model:value="budgetInfo.constructionUnit"
                  placeholder="请输入建设单位"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="预算编制人">
                <a-input
                  v-model:value="budgetInfo.compiler"
                  placeholder="请输入预算编制人"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 预算特有字段 -->
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="预算定额标准">
                <a-select
                  v-model:value="budgetInfo.quotaStandard"
                  placeholder="请选择预算定额标准"
                >
                  <a-select-option value="national">国家定额</a-select-option>
                  <a-select-option value="local">地方定额</a-select-option>
                  <a-select-option value="enterprise">企业定额</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="预算准确度等级">
                <a-select
                  v-model:value="budgetInfo.accuracyLevel"
                  placeholder="请选择准确度等级"
                >
                  <a-select-option value="A">A级（±10%）</a-select-option>
                  <a-select-option value="B">B级（±15%）</a-select-option>
                  <a-select-option value="C">C级（±25%）</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 预算统计信息 -->
      <a-card title="预算统计信息" class="stats-card">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-statistic
              title="预算项目数"
              :value="budgetStats.itemCount"
              :value-style="{ color: '#3f8600' }"
              suffix="项"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="直接费用"
              :value="budgetStats.directCost"
              :precision="2"
              suffix="万元"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="间接费用"
              :value="budgetStats.indirectCost"
              :precision="2"
              suffix="万元"
              :value-style="{ color: '#722ed1' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="利润和税金"
              :value="budgetStats.profitAndTax"
              :precision="2"
              suffix="万元"
              :value-style="{ color: '#faad14' }"
            />
          </a-col>
        </a-row>
      </a-card>

      <!-- 预算进度跟踪 -->
      <a-card title="预算编制进度" class="progress-card">
        <div class="progress-item">
          <div class="progress-label">分部分项工程费</div>
          <a-progress
            :percent="budgetProgress.itemWork"
            status="active"
            :stroke-color="{ from: '#108ee9', to: '#87d068' }"
          />
        </div>
        <div class="progress-item">
          <div class="progress-label">措施项目费</div>
          <a-progress
            :percent="budgetProgress.measureWork"
            :stroke-color="{ '0%': '#108ee9', '100%': '#87d068' }"
          />
        </div>
        <div class="progress-item">
          <div class="progress-label">其他项目费</div>
          <a-progress :percent="budgetProgress.otherWork" />
        </div>
        <div class="progress-item">
          <div class="progress-label">规费和税金</div>
          <a-progress :percent="budgetProgress.feeAndTax" />
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { SaveOutlined, ExportOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

const props = defineProps({
  moduleType: {
    type: String,
    required: true
  },
  levelType: {
    type: Number,
    required: true
  },
  tabInfo: {
    type: Object,
    default: () => ({})
  },
  projectData: {
    type: Object,
    default: () => ({})
  },
  tableData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 预算基本信息
const budgetInfo = ref({
  name: '',
  code: '',
  totalAmount: null,
  compileDate: dayjs(),
  constructionUnit: '',
  compiler: '',
  quotaStandard: 'national',
  accuracyLevel: 'A'
})

// 预算统计信息
const budgetStats = ref({
  itemCount: 0,
  directCost: 0,
  indirectCost: 0,
  profitAndTax: 0
})

// 预算编制进度
const budgetProgress = ref({
  itemWork: 85,
  measureWork: 70,
  otherWork: 60,
  feeAndTax: 90
})

// 处理保存
const handleSave = () => {
  // 预算特有的保存逻辑
  const budgetData = {
    ...budgetInfo.value,
    compileDate: budgetInfo.value.compileDate?.format?.('YYYY-MM-DD'),
    stats: budgetStats.value,
    progress: budgetProgress.value
  }

  emit('dataChange', budgetData)
  emit('action', 'save', budgetData)
  message.success('预算概况保存成功')
}

// 处理导出
const handleExport = () => {
  emit('action', 'export', {
    type: 'budget-overview',
    data: {
      info: budgetInfo.value,
      stats: budgetStats.value,
      progress: budgetProgress.value
    }
  })
  message.success('预算报告导出成功')
}

// 计算统计数据
const calculateStats = () => {
  if (props.tableData && props.tableData.length > 0) {
    const totalAmount = props.tableData.reduce((sum, item) => sum + (item.amount || 0), 0)

    budgetStats.value = {
      itemCount: props.tableData.length,
      directCost: totalAmount * 0.7 / 10000, // 直接费约占70%
      indirectCost: totalAmount * 0.2 / 10000, // 间接费约占20%
      profitAndTax: totalAmount * 0.1 / 10000  // 利润税金约占10%
    }

    // 更新总预算金额
    budgetInfo.value.totalAmount = totalAmount
  }
}

// 监听数据变化
watch(() => props.tableData, () => {
  calculateStats()
}, { deep: true })

// 监听表单数据变化
watch(budgetInfo, (newData) => {
  emit('dataChange', newData)
}, { deep: true })

onMounted(() => {
  // 初始化预算数据
  if (props.projectData && Object.keys(props.projectData).length > 0) {
    Object.assign(budgetInfo.value, props.projectData)
  }

  calculateStats()
})
</script>

<style lang="scss" scoped>
.budget-project-overview {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;

    h3 {
      margin: 0;
      color: #333;
      font-weight: 600;
    }

    .page-actions {
      display: flex;
      gap: 8px;
    }
  }

  .page-body {
    flex: 1;
    overflow-y: auto;
    padding: 16px 24px;
    background: #f5f5f5;

    .info-card,
    .stats-card,
    .progress-card {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .stats-card {
      :deep(.ant-statistic-content) {
        font-size: 18px;
      }
    }

    .progress-card {
      .progress-item {
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        .progress-label {
          margin-bottom: 8px;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }
}
</style>