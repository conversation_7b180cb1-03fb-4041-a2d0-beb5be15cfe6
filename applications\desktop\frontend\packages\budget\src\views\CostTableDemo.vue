<!--
  预算模块使用新CostTable组件的示例
-->
<template>
  <div class="cost-table-demo">
    <SimpleHeader
      title="预算编制 - 分部分项工程"
      :show-window-controls="true"
      @minimize="handleMinimize"
      @maximize="handleMaximize"
      @close="handleClose"
    />

    <div class="demo-content">
      <!-- 工具栏 -->
      <div class="toolbar">
        <a-space>
          <a-button type="primary" @click="addProject">
            <template #icon><PlusOutlined /></template>
            新建项目
          </a-button>
          <a-button @click="importData">
            <template #icon><ImportOutlined /></template>
            导入数据
          </a-button>
          <a-button @click="exportData">
            <template #icon><ExportOutlined /></template>
            导出数据
          </a-button>
          <a-divider type="vertical" />
          <a-button @click="saveData" :loading="saving">
            <template #icon><SaveOutlined /></template>
            保存
          </a-button>
        </a-space>

        <a-space>
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索项目名称或编码"
            style="width: 250px"
            @search="handleSearch"
          />
          <a-select v-model:value="viewMode" style="width: 120px">
            <a-select-option value="tree">树形视图</a-select-option>
            <a-select-option value="list">列表视图</a-select-option>
          </a-select>
        </a-space>
      </div>

      <!-- 统一的CostTable组件 -->
      <div class="table-wrapper">
        <CostTable
          ref="costTableRef"
          :data="budgetData"
          module-type="yuSuan"
          :loading="loading"
          :editable="true"
          :range-selection="true"
          :bordered="true"
          table-size="small"
          :scroll-config="{ x: 1400, y: 'calc(100vh - 200px)' }"
          :tree-code-field="'bdCode'"
          @data-change="handleDataChange"
          @row-select="handleRowSelect"
          @cell-edit="handleCellEdit"
          @cell-edited="handleCellEdited"
          @add-row="handleAddRow"
          @delete-row="handleDeleteRow"
          @copy-rows="handleCopyRows"
          @paste-rows="handlePasteRows"
          @component-action="handleComponentAction"
          @record-update="handleRecordUpdate"
        >
          <!-- 自定义金额单元格 -->
          <template #cell-amount="{ record, column, text }">
            <span class="amount-cell" :class="{ 'negative': text < 0 }">
              ¥{{ formatAmount(text) }}
            </span>
          </template>

          <!-- 自定义状态单元格 -->
          <template #cell-status="{ record, column, text }">
            <a-tag :color="getStatusColor(text)">
              {{ getStatusText(text) }}
            </a-tag>
          </template>
        </CostTable>
      </div>

      <!-- 汇总信息 -->
      <div class="summary-bar">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-statistic
              title="项目总数"
              :value="summaryData.totalCount"
              suffix="个"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="预算总额"
              :value="summaryData.totalAmount"
              :precision="2"
              prefix="¥"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="已选项目"
              :value="selectedCount"
              suffix="个"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="选中金额"
              :value="selectedAmount"
              :precision="2"
              prefix="¥"
            />
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ImportOutlined,
  ExportOutlined,
  SaveOutlined
} from '@ant-design/icons-vue'
import { 
  CostTable, 
  SimpleHeader,
  useWindowControls 
} from '@cost-app/shared-components'

// 响应式数据
const costTableRef = ref(null)
const loading = ref(false)
const saving = ref(false)
const searchText = ref('')
const viewMode = ref('tree')
const selectedRows = ref([])
const budgetData = ref([])

// 窗口控制
const { handleMinimize, handleMaximize, handleClose } = useWindowControls()

// 计算属性
const selectedCount = computed(() => selectedRows.value.length)
const selectedAmount = computed(() => {
  return selectedRows.value.reduce((sum, row) => sum + (row.amount || 0), 0)
})

const summaryData = computed(() => {
  const flatData = flattenTreeData(budgetData.value)
  return {
    totalCount: flatData.length,
    totalAmount: flatData.reduce((sum, item) => sum + (item.amount || 0), 0)
  }
})

// 示例数据
const generateBudgetData = () => {
  return [
    {
      id: 1,
      kind: '01',
      bdCode: '01',
      name: '建筑工程',
      projectAttr: '',
      specification: '',
      unit: '',
      quantity: 0,
      price: 0,
      amount: 2850000,
      status: 'active',
      level: 0,
      children: [
        {
          id: 2,
          kind: '02',
          bdCode: '0101',
          name: '土建工程',
          projectAttr: '',
          specification: '',
          unit: '',
          quantity: 0,
          price: 0,
          amount: 1500000,
          status: 'active',
          level: 1,
          children: [
            {
              id: 3,
              kind: '03',
              bdCode: '010101001',
              name: '挖土方',
              projectAttr: '机械挖土',
              specification: '三类土',
              unit: 'm³',
              quantity: 1000,
              price: 25.50,
              amount: 25500,
              status: 'active',
              level: 2
            },
            {
              id: 4,
              kind: '03',
              bdCode: '010101002',
              name: '基础垫层',
              projectAttr: 'C15混凝土垫层',
              specification: '厚度100mm',
              unit: 'm³',
              quantity: 50,
              price: 280.00,
              amount: 14000,
              status: 'active',
              level: 2
            }
          ]
        },
        {
          id: 5,
          kind: '02',
          bdCode: '0102',
          name: '装饰工程',
          projectAttr: '',
          specification: '',
          unit: '',
          quantity: 0,
          price: 0,
          amount: 1350000,
          status: 'active',
          level: 1,
          children: [
            {
              id: 6,
              kind: '03',
              bdCode: '010201001',
              name: '内墙涂料',
              projectAttr: '乳胶漆',
              specification: '两遍成活',
              unit: 'm²',
              quantity: 2000,
              price: 15.80,
              amount: 31600,
              status: 'active',
              level: 2
            }
          ]
        }
      ]
    },
    {
      id: 7,
      kind: '01',
      bdCode: '02',
      name: '安装工程',
      projectAttr: '',
      specification: '',
      unit: '',
      quantity: 0,
      price: 0,
      amount: 850000,
      status: 'active',
      level: 0,
      children: [
        {
          id: 8,
          kind: '02',
          bdCode: '0201',
          name: '给排水工程',
          projectAttr: '',
          specification: '',
          unit: '',
          quantity: 0,
          price: 0,
          amount: 450000,
          status: 'active',
          level: 1,
          children: [
            {
              id: 9,
              kind: '03',
              bdCode: '020101001',
              name: '给水管道安装',
              projectAttr: 'PPR管',
              specification: 'DN25',
              unit: 'm',
              quantity: 500,
              price: 35.00,
              amount: 17500,
              status: 'active',
              level: 2
            }
          ]
        }
      ]
    }
  ]
}

// 方法
const flattenTreeData = (data) => {
  const result = []
  const traverse = (items) => {
    items.forEach(item => {
      result.push(item)
      if (item.children && item.children.length > 0) {
        traverse(item.children)
      }
    })
  }
  traverse(data)
  return result
}

const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

const getStatusColor = (status) => {
  const colors = {
    active: 'green',
    pending: 'orange',
    completed: 'blue',
    cancelled: 'red'
  }
  return colors[status] || 'default'
}

const getStatusText = (status) => {
  const texts = {
    active: '进行中',
    pending: '待审核',
    completed: '已完成',
    cancelled: '已取消'
  }
  return texts[status] || '未知'
}

// 事件处理
const handleDataChange = (data) => {
  console.log('数据变更:', data)
  budgetData.value = data
}

const handleRowSelect = (rows) => {
  selectedRows.value = rows
  console.log('选中行:', rows)
}

const handleCellEdit = (editData) => {
  console.log('开始编辑:', editData)
}

const handleCellEdited = (editData) => {
  console.log('编辑完成:', editData)
  message.success('编辑成功')
}

const handleAddRow = (addData) => {
  console.log('新增行:', addData)
  message.success('新增成功')
}

const handleDeleteRow = (record) => {
  console.log('删除行:', record)
  message.success('删除成功')
}

const handleCopyRows = (rows) => {
  console.log('复制行:', rows)
  message.success(`已复制 ${rows.length} 项`)
}

const handlePasteRows = (pasteData) => {
  console.log('粘贴行:', pasteData)
  message.success(`已粘贴 ${pasteData.rows.length} 项`)
}

const handleComponentAction = (actionData) => {
  console.log('组件操作:', actionData)
  
  switch (actionData.type) {
    case 'codeReset':
      message.success('编码重置完成')
      break
    case 'syncNameToDE':
      message.success('名称同步完成')
      break
    case 'bcQd':
      message.success('补充清单完成')
      break
    case 'importExcel':
      message.success('Excel导入完成')
      break
    default:
      message.info(`执行操作: ${actionData.type}`)
  }
}

const handleRecordUpdate = (updateData) => {
  console.log('记录更新:', updateData)
}

const addProject = () => {
  message.info('新建项目功能')
}

const importData = () => {
  message.info('导入数据功能')
}

const exportData = () => {
  message.info('导出数据功能')
}

const saveData = async () => {
  saving.value = true
  try {
    // 模拟保存
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('保存成功')
  } catch (error) {
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleSearch = (value) => {
  console.log('搜索:', value)
}

// 初始化
onMounted(() => {
  budgetData.value = generateBudgetData()
})
</script>

<style scoped>
.cost-table-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.demo-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
}

.table-wrapper {
  flex: 1;
  min-height: 0;
  margin-bottom: 16px;
}

.summary-bar {
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.amount-cell {
  font-weight: 600;
  color: #1890ff;
}

.amount-cell.negative {
  color: #ff4d4f;
}
</style>
