/**
 * 项目概况基本信息模拟数据
 * 参考原有项目的数据结构和字段
 */

// 基本信息模拟数据
export const mockBasicInfoData = [
  // 基本信息分组
  {
    sequenceNbr: 1,
    dispNo: '1',
    name: '基本信息',
    remark: '',
    addFlag: 0,
    lockFlag: 0,
    type: 'title',
    groupCode: 1,
    parentId: null,
    childrenList: [
      {
        sequenceNbr: 2,
        dispNo: '1.1',
        name: '项目名称',
        remark: '某住宅小区建设项目',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        childrenList: []
      },
      {
        sequenceNbr: 3,
        dispNo: '1.2',
        name: '项目编号',
        remark: 'PROJ-2024-001',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        childrenList: []
      },
      {
        sequenceNbr: 4,
        dispNo: '1.3',
        name: '建设单位',
        remark: '某房地产开发有限公司',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        childrenList: []
      },
      {
        sequenceNbr: 5,
        dispNo: '1.4',
        name: '设计单位',
        remark: '某建筑设计研究院',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        childrenList: []
      },
      {
        sequenceNbr: 6,
        dispNo: '1.5',
        name: '建筑面积',
        remark: '15000.00',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        childrenList: [],
        jsonStr: ['10000.00', '15000.00', '20000.00', '25000.00'] // 可选值
      },
      {
        sequenceNbr: 7,
        dispNo: '1.6',
        name: '工程规模',
        remark: '大型',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        childrenList: [],
        jsonStr: ['小型', '中型', '大型', '特大型']
      },
      {
        sequenceNbr: 8,
        dispNo: '1.7',
        name: '编制时间',
        remark: '2024-01-15',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        childrenList: []
      },
      {
        sequenceNbr: 9,
        dispNo: '1.8',
        name: '编制人',
        remark: '张工程师',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        childrenList: []
      },
      {
        sequenceNbr: 10,
        dispNo: '1.9',
        name: '核对人(复核人)',
        remark: '李总工',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        childrenList: []
      },
      {
        sequenceNbr: 11,
        dispNo: '1.10',
        name: '核对(复核)时间',
        remark: '2024-01-20',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        childrenList: []
      }
    ]
  },
  
  // 工程所在地分组
  {
    sequenceNbr: 12,
    dispNo: '2',
    name: '工程所在地',
    remark: '',
    addFlag: 0,
    lockFlag: 0,
    type: 'title',
    groupCode: 2,
    parentId: null,
    childrenList: [
      {
        sequenceNbr: 13,
        dispNo: '2.1',
        name: '省份',
        remark: '广东省',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 2,
        parentId: 12,
        childrenList: []
      },
      {
        sequenceNbr: 14,
        dispNo: '2.2',
        name: '城市',
        remark: '深圳市',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 2,
        parentId: 12,
        childrenList: []
      },
      {
        sequenceNbr: 15,
        dispNo: '2.3',
        name: '区县',
        remark: '南山区',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 2,
        parentId: 12,
        childrenList: []
      },
      {
        sequenceNbr: 16,
        dispNo: '2.4',
        name: '详细地址',
        remark: '科技园南区某路某号',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 2,
        parentId: 12,
        childrenList: []
      }
    ]
  },

  // 招标信息分组
  {
    sequenceNbr: 17,
    dispNo: '3',
    name: '招标信息',
    remark: '',
    addFlag: 0,
    lockFlag: 0,
    type: 'title',
    groupCode: 3,
    parentId: null,
    childrenList: [
      {
        sequenceNbr: 18,
        dispNo: '3.1',
        name: '招标人(发包人)',
        remark: '某房地产开发有限公司',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 3,
        parentId: 17,
        childrenList: []
      },
      {
        sequenceNbr: 19,
        dispNo: '3.2',
        name: '招标人(发包人)法人或其授权人',
        remark: '王总经理',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 3,
        parentId: 17,
        childrenList: []
      },
      {
        sequenceNbr: 20,
        dispNo: '3.3',
        name: '工程造价咨询人',
        remark: '某造价咨询有限公司',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 3,
        parentId: 17,
        childrenList: []
      },
      {
        sequenceNbr: 21,
        dispNo: '3.4',
        name: '工程造价咨询人法人或其授权人',
        remark: '刘总工程师',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 3,
        parentId: 17,
        childrenList: []
      }
    ]
  },

  // 投标信息分组
  {
    sequenceNbr: 22,
    dispNo: '4',
    name: '投标信息',
    remark: '',
    addFlag: 0,
    lockFlag: 0,
    type: 'title',
    groupCode: 4,
    parentId: null,
    childrenList: [
      {
        sequenceNbr: 23,
        dispNo: '4.1',
        name: '投标人(承包人)',
        remark: '某建筑工程有限公司',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 4,
        parentId: 22,
        childrenList: []
      },
      {
        sequenceNbr: 24,
        dispNo: '4.2',
        name: '投标人(承包人)法人或其授权人',
        remark: '陈项目经理',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 4,
        parentId: 22,
        childrenList: []
      },
      {
        sequenceNbr: 25,
        dispNo: '4.3',
        name: '开工日期',
        remark: '2024-03-01',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 4,
        parentId: 22,
        childrenList: []
      },
      {
        sequenceNbr: 26,
        dispNo: '4.4',
        name: '竣工日期',
        remark: '2025-12-31',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 4,
        parentId: 22,
        childrenList: []
      }
    ]
  }
]

// 根据模块类型获取特定的基本信息数据
export function getBasicInfoByModule(moduleType) {
  const baseData = JSON.parse(JSON.stringify(mockBasicInfoData))
  
  // 根据不同模块添加特有字段
  switch (moduleType) {
    case 'budget':
      // 预算模块特有字段
      baseData[0].childrenList.push(
        {
          sequenceNbr: 27,
          dispNo: '1.11',
          name: '定额标准',
          remark: '国家标准',
          addFlag: 0,
          lockFlag: 0,
          type: 'item',
          groupCode: 1,
          parentId: 1,
          childrenList: [],
          jsonStr: ['国家标准', '地方标准', '企业标准']
        },
        {
          sequenceNbr: 28,
          dispNo: '1.12',
          name: '计价模式',
          remark: '工程量清单计价',
          addFlag: 0,
          lockFlag: 0,
          type: 'item',
          groupCode: 1,
          parentId: 1,
          childrenList: [],
          jsonStr: ['工程量清单计价', '定额计价', '混合计价']
        }
      )
      break
      
    case 'settlement':
      // 结算模块特有字段
      baseData[0].childrenList.push(
        {
          sequenceNbr: 29,
          dispNo: '1.11',
          name: '结算类型',
          remark: '竣工结算',
          addFlag: 0,
          lockFlag: 0,
          type: 'item',
          groupCode: 1,
          parentId: 1,
          childrenList: [],
          jsonStr: ['竣工结算', '阶段结算', '专项结算']
        },
        {
          sequenceNbr: 30,
          dispNo: '1.12',
          name: '合同金额',
          remark: '5000.00',
          addFlag: 0,
          lockFlag: 0,
          type: 'item',
          groupCode: 1,
          parentId: 1,
          childrenList: []
        }
      )
      break
      
    case 'review':
      // 审核模块特有字段
      baseData[0].childrenList.push(
        {
          sequenceNbr: 31,
          dispNo: '1.11',
          name: '审核类型',
          remark: '预算审核',
          addFlag: 0,
          lockFlag: 0,
          type: 'item',
          groupCode: 1,
          parentId: 1,
          childrenList: [],
          jsonStr: ['预算审核', '结算审核', '变更审核']
        },
        {
          sequenceNbr: 32,
          dispNo: '1.12',
          name: '审核结果',
          remark: '通过',
          addFlag: 0,
          lockFlag: 0,
          type: 'item',
          groupCode: 1,
          parentId: 1,
          childrenList: [],
          jsonStr: ['通过', '不通过', '需修改']
        }
      )
      break
  }
  
  return baseData
}
