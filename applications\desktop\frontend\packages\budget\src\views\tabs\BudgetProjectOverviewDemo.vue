<template>
  <div class="budget-project-overview-demo">
    <!-- 使用新的ProjectOverview共享组件 -->
    <ProjectOverview
      module-type="budget"
      :level-type="levelType"
      :project-data="projectData"
      :tab-info="tabInfo"
      :editable="editable"
      :custom-config="customConfig"
      :permissions="permissions"
      @data-change="handleDataChange"
      @save="handleSave"
      @export="handleExport"
      @lock="handleLock"
      @action="handleAction"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ProjectOverview } from '@cost-app/shared-components'

const props = defineProps({
  levelType: {
    type: Number,
    default: 3
  },
  tabInfo: {
    type: Object,
    default: () => ({ code: 'project-overview', name: '项目概况' })
  },
  projectId: {
    type: String,
    default: 'demo-project-001'
  },
  unitId: {
    type: String,
    default: 'demo-unit-001'
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 响应式数据
const editable = ref(true)
const projectData = ref({
  projectName: '某住宅小区建设项目',
  projectCode: 'PROJ-2024-001',
  buildingArea: 15000.00,
  projectScale: '大型',
  constructionUnit: '某房地产开发有限公司',
  designUnit: '某建筑设计研究院',
  compileDate: '2024-01-15',
  compiler: '张工程师',
  reviewer: '李总工',
  reviewDate: '2024-01-20',
  // 预算特有字段
  quotaStandard: 'national',
  pricingMode: 'list',
  budgetType: 'construction'
})

// 自定义配置
const customConfig = computed(() => ({
  // 预算模块特定配置
  hiddenFields: [], // 隐藏的字段
  validationRules: {
    projectName: [
      { required: true, message: '项目名称不能为空' }
    ],
    buildingArea: [
      { required: true, message: '建筑面积不能为空' },
      { type: 'number', min: 0, message: '建筑面积必须大于0' }
    ]
  },
  // 预算特有字段配置
  extraFields: {
    quotaStandard: {
      name: '定额标准',
      type: 'select',
      required: true,
      options: [
        { label: '国家标准', value: 'national' },
        { label: '地方标准', value: 'local' },
        { label: '企业标准', value: 'enterprise' }
      ]
    },
    pricingMode: {
      name: '计价模式',
      type: 'select',
      required: true,
      options: [
        { label: '工程量清单计价', value: 'list' },
        { label: '定额计价', value: 'quota' },
        { label: '混合计价', value: 'mixed' }
      ]
    },
    budgetType: {
      name: '预算类型',
      type: 'select',
      required: true,
      options: [
        { label: '施工图预算', value: 'construction' },
        { label: '招标控制价', value: 'tender' },
        { label: '投标报价', value: 'bid' }
      ]
    }
  }
}))

// 权限配置
const permissions = computed(() => ({
  canEdit: true,
  canDelete: true,
  canLock: true,
  canExport: true,
  canImport: false
}))

// 事件处理
const handleDataChange = (data) => {
  console.log('预算概况数据变更:', data)
  
  // 更新本地数据
  if (data.type === 'basic') {
    updateProjectData(data.data)
  } else if (data.type === 'feature') {
    updateFeatureData(data.data)
  }
  
  emit('dataChange', data)
}

const handleSave = (data) => {
  console.log('保存预算概况:', data)
  
  // 预算模块特有的保存逻辑
  const saveData = {
    ...data,
    moduleType: 'budget',
    saveTime: new Date().toISOString()
  }
  
  // 模拟API调用
  setTimeout(() => {
    message.success('预算概况保存成功')
    console.log('保存的数据:', saveData)
  }, 1000)
  
  emit('action', 'save', saveData)
}

const handleExport = (data) => {
  console.log('导出预算概况:', data)
  
  // 预算模块特有的导出逻辑
  const exportData = {
    ...data,
    exportType: 'budget-overview',
    exportTime: new Date().toISOString()
  }
  
  // 模拟导出
  setTimeout(() => {
    message.success('预算概况导出成功')
    console.log('导出的数据:', exportData)
  }, 500)
  
  emit('action', 'export', exportData)
}

const handleLock = (lockStatus) => {
  console.log('锁定状态变更:', lockStatus)
  
  // 模拟锁定操作
  setTimeout(() => {
    message.success(lockStatus ? '锁定成功' : '解锁成功')
  }, 300)
  
  emit('action', 'lock', lockStatus)
}

const handleAction = (action, data) => {
  console.log('预算概况操作:', action, data)
  emit('action', action, data)
}

// 业务逻辑函数
const updateProjectData = (data) => {
  Object.assign(projectData.value, data)
}

const updateFeatureData = (data) => {
  console.log('更新工程特征:', data)
}

// 数据加载函数
const loadProjectData = async () => {
  if (!props.projectId) return
  
  try {
    // 模拟加载项目基本数据
    console.log('加载项目数据:', props.projectId)
    
    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 模拟数据更新
    projectData.value = {
      ...projectData.value,
      lastUpdateTime: new Date().toISOString()
    }
    
    message.success('项目数据加载成功')
  } catch (error) {
    console.error('加载项目数据失败:', error)
    message.error('加载数据失败')
  }
}

// 监听器
watch(() => props.projectId, () => {
  // 项目ID变化时重新加载数据
  loadProjectData()
}, { immediate: true })

// 生命周期
onMounted(() => {
  console.log('预算概况演示组件已挂载')
  console.log('当前配置:', {
    moduleType: 'budget',
    levelType: props.levelType,
    projectId: props.projectId,
    customConfig: customConfig.value,
    permissions: permissions.value
  })
})
</script>

<style lang="scss" scoped>
.budget-project-overview-demo {
  height: 100%;
  
  // 预算模块特有样式
  :deep(.project-overview) {
    .page-header h3 {
      color: #52c41a; // 预算模块主色调
    }
    
    .ant-tabs-tab {
      &.ant-tabs-tab-active {
        color: #52c41a;
      }
    }
    
    .field-highlight {
      color: #52c41a;
    }
  }
}
</style>
