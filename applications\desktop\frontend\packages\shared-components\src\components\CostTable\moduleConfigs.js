/**
 * CostTable 模块配置管理
 * 统一管理概预结审工料机五个模块的差异配置
 */

// 模块基础配置
export const MODULE_CONFIGS = {
  // 概算模块
  gaiSuan: {
    name: '概算',
    codeField: 'deCode',
    nameField: 'deName',
    apiModule: 'gSdetailApi',
    hooks: 'useGsSubItem',
    color: '#1890ff',
    features: {
      hasCheckbox: false,
      hasProjectAttr: false,
      hasSpecification: true,
      hasQuantityExpression: true,
      hasResqtyExp: true,
      supportsTempRemove: false,
      supportsLock: true,
      supportsIntroductionQuantity: true,
      supportsBatchModifyMainMaterials: true
    }
  },
  
  // 预算模块
  yuSuan: {
    name: '预算',
    codeField: 'bdCode',
    nameField: 'name',
    apiModule: 'detailApi',
    hooks: 'useSubItemStable',
    color: '#52c41a',
    features: {
      hasCheckbox: true,
      hasProjectAttr: true,
      hasSpecification: true,
      hasQuantityExpression: false,
      hasResqtyExp: false,
      supportsTempRemove: false,
      supportsLock: true,
      supportsCodeReset: true,
      supportsSyncNameToDE: true
    }
  },
  
  // 结算模块
  jieSuan: {
    name: '结算',
    codeField: 'bdCode',
    nameField: 'name',
    apiModule: 'detailApi',
    hooks: 'useSubItemStable',
    color: '#722ed1',
    features: {
      hasCheckbox: true,
      hasProjectAttr: true,
      hasSpecification: true,
      hasQuantityExpression: false,
      hasResqtyExp: false,
      supportsTempRemove: false,
      supportsLock: true,
      supportsContractAssociation: true,
      supportsProgressSettlement: true
    }
  },
  
  // 审核模块
  shenHe: {
    name: '审核',
    codeField: 'bdCode',
    nameField: 'name',
    apiModule: 'shApi',
    hooks: 'useSubItemStable',
    color: '#faad14',
    features: {
      hasCheckbox: true,
      hasProjectAttr: true,
      hasSpecification: true,
      hasQuantityExpression: false,
      hasResqtyExp: false,
      supportsTempRemove: true,
      supportsLock: true,
      supportsAuditComparison: true,
      supportsAuditOpinion: true
    }
  },
  
  // 工料机模块
  gongLiaoJi: {
    name: '工料机',
    codeField: 'deCode',
    nameField: 'deName',
    apiModule: 'gljdetailApi',
    hooks: 'useGljSubItem',
    color: '#eb2f96',
    features: {
      hasCheckbox: false,
      hasProjectAttr: false,
      hasSpecification: true,
      hasQuantityExpression: false,
      hasResqtyExp: true,
      supportsTempRemove: false,
      supportsLock: true,
      supportsStandardConversion: true,
      supportsSetMainMaterial: true,
      supportsProjectAttrAssociation: true
    }
  }
}

// 列配置生成器
export const generateColumnConfig = (moduleType) => {
  const config = MODULE_CONFIGS[moduleType]
  if (!config) {
    throw new Error(`Unknown module type: ${moduleType}`)
  }

  const baseColumns = []

  // 复选框列（预算/结算/审核）
  if (config.features.hasCheckbox) {
    baseColumns.push({
      field: 'selection',
      title: '',
      width: 40,
      fixed: 'left',
      type: 'checkbox',
      resizable: false
    })
  }

  // 序号列
  baseColumns.push({
    field: 'dispNo',
    title: '序号',
    width: config.features.hasCheckbox ? 40 : 50,
    fixed: 'left',
    resizable: false
  })

  // 编码列（树形结构）
  baseColumns.push({
    field: config.codeField,
    title: moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi' ? '定额编码' : '项目编码',
    width: moduleType === 'gaiSuan' ? 190 : 170,
    editable: true,
    treeNode: true
  })

  // 类别/类型列
  if (moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi') {
    baseColumns.push({
      field: 'type',
      title: '类别',
      width: 65,
      editable: true
    })
  } else {
    baseColumns.push({
      field: 'type',
      title: '类型',
      width: 60,
      editable: true
    })
  }

  // 名称列
  baseColumns.push({
    field: config.nameField,
    title: moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi' ? '定额名称' : '项目名称',
    width: moduleType === 'gaiSuan' ? 260 : 200,
    editable: true
  })

  // 项目特征列（预算/结算/审核）
  if (config.features.hasProjectAttr) {
    baseColumns.push({
      field: 'projectAttr',
      title: '项目特征',
      width: 180,
      editable: true
    })
  }

  // 规格型号列
  if (config.features.hasSpecification) {
    baseColumns.push({
      field: 'specification',
      title: '规格型号',
      width: 80,
      editable: true,
      hidden: moduleType === 'gaiSuan' // 概算模块默认隐藏
    })
  }

  // 单位列
  baseColumns.push({
    field: 'unit',
    title: '单位',
    width: 70,
    editable: true
  })

  // 消耗量列（概算/工料机）
  if (config.features.hasResqtyExp) {
    baseColumns.push({
      field: 'resqtyExp',
      title: '消耗量',
      width: 100,
      editable: true
    })
  }

  // 工程量表达式列（概算）
  if (config.features.hasQuantityExpression) {
    baseColumns.push({
      field: 'quantityExpression',
      title: '工程量表达式',
      width: 120,
      editable: true
    })
  }

  return baseColumns
}

// 字段编辑配置生成器
export const generateFieldEditConfig = (moduleType, fieldName, record) => {
  const config = MODULE_CONFIGS[moduleType]

  // 基础编辑配置
  const baseEditConfig = {
    editable: true,
    required: false,
    validator: null,
    component: 'input', // input, select, checkbox, number, textarea
    componentProps: {}
  }

  // 模块特定的字段编辑规则
  const fieldEditRules = {
    // 概算模块
    gaiSuan: {
      quantity: {
        editable: true,
        component: 'number',
        componentProps: { precision: 3, min: 0 }
      },
      deCode: { editable: true, required: true },
      deName: { editable: true, required: true }
    },

    // 预算模块
    yuSuan: {
      quantity: {
        editable: true,
        component: 'number',
        componentProps: { precision: 3, min: 0 }
      },
      budgetUnitPrice: {
        editable: true,
        component: 'number',
        componentProps: { precision: 2, min: 0 }
      },
      bdCode: { editable: true, required: true },
      name: { editable: true, required: true }
    },

    // 结算模块
    jieSuan: {
      quantity: {
        editable: (context) => {
          // 结算中工程量编辑需要区分合同内外
          return context.record.contractType === 'within' ||
                 context.record.isContractExternal === true
        },
        component: 'number',
        componentProps: { precision: 3, min: 0 }
      },
      contractQuantity: {
        editable: false, // 合同工程量不可编辑
        component: 'number',
        componentProps: { precision: 3, disabled: true }
      },
      settlementUnitPrice: {
        editable: true,
        component: 'number',
        componentProps: { precision: 2, min: 0 }
      },
      bdCode: { editable: false }, // 结算中项目编码不可编辑
      name: { editable: false }
    },

    // 审核模块
    shenHe: {
      quantity: {
        editable: false, // 审核中工程量不可编辑
        component: 'number',
        componentProps: { precision: 3, disabled: true }
      },
      auditQuantity: {
        editable: true, // 审核工程量可编辑
        component: 'number',
        componentProps: { precision: 3, min: 0 }
      },
      auditUnitPrice: {
        editable: true,
        component: 'number',
        componentProps: { precision: 2, min: 0 }
      },
      auditOpinion: {
        editable: true,
        component: 'textarea',
        componentProps: { rows: 2, maxLength: 200 }
      },
      bdCode: { editable: false },
      name: { editable: false }
    }
  }

  const moduleRules = fieldEditRules[moduleType] || {}
  const fieldRule = moduleRules[fieldName] || baseEditConfig

  // 如果editable是函数，执行它
  if (typeof fieldRule.editable === 'function') {
    fieldRule.editable = fieldRule.editable({ record, config, moduleType })
  }

  return {
    ...baseEditConfig,
    ...fieldRule
  }
}

// 模块特定字段配置
export const MODULE_FIELD_CONFIGS = {
  // 概算模块字段
  gaiSuan: {
    fields: [
      { field: 'deCode', title: '定额编码', width: 190, required: true },
      { field: 'deName', title: '定额名称', width: 260, required: true },
      { field: 'specification', title: '规格型号', width: 80, hidden: true },
      { field: 'unit', title: '单位', width: 70 },
      { field: 'quantity', title: '工程量', width: 120, dataType: 'number' },
      { field: 'unitPrice', title: '单价', width: 120, dataType: 'number' },
      { field: 'amount', title: '合价', width: 150, dataType: 'number', editable: false }
    ]
  },

  // 预算模块字段
  yuSuan: {
    fields: [
      { field: 'bdCode', title: '项目编码', width: 150, required: true, autoHeight: true },
      { field: 'name', title: '项目名称', width: 180, required: true, autoHeight: true },
      { field: 'projectAttr', title: '项目特征', width: 160, autoHeight: true },
      { field: 'specification', title: '规格型号', width: 80, autoHeight: true },
      { field: 'unit', title: '单位', width: 60 },
      { field: 'quantity', title: '工程量', width: 100, dataType: 'number' },
      { field: 'budgetUnitPrice', title: '预算单价', width: 100, dataType: 'number' },
      { field: 'budgetAmount', title: '预算合价', width: 120, dataType: 'number', editable: false },
      { field: 'quotaCode', title: '定额编号', width: 100, autoHeight: true },
      { field: 'quotaUnitPrice', title: '定额单价', width: 100, dataType: 'number' },
      { field: 'laborCost', title: '人工费', width: 90, dataType: 'number', editable: false },
      { field: 'materialCost', title: '材料费', width: 90, dataType: 'number', editable: false },
      { field: 'mechanicalCost', title: '机械费', width: 90, dataType: 'number', editable: false }
    ]
  },

  // 结算模块字段
  jieSuan: {
    fields: [
      { field: 'bdCode', title: '项目编码', width: 170, editable: false },
      { field: 'name', title: '项目名称', width: 200, editable: false },
      { field: 'projectAttr', title: '项目特征', width: 180, editable: false },
      { field: 'unit', title: '单位', width: 70, editable: false },
      { field: 'contractQuantity', title: '合同工程量', width: 120, dataType: 'number', editable: false },
      { field: 'quantity', title: '实际工程量', width: 120, dataType: 'number', editableCondition: 'contractType' },
      { field: 'contractUnitPrice', title: '合同单价', width: 120, dataType: 'number', editable: false },
      { field: 'settlementUnitPrice', title: '结算单价', width: 120, dataType: 'number' },
      { field: 'settlementAmount', title: '结算合价', width: 150, dataType: 'number', editable: false },
      { field: 'contractType', title: '合同类型', width: 100, component: 'select',
        options: [
          { value: 'within', label: '合同内' },
          { value: 'external', label: '合同外' },
          { value: 'change', label: '变更' }
        ]
      }
    ]
  },

  // 审核模块字段
  shenHe: {
    fields: [
      { field: 'bdCode', title: '项目编码', width: 170, editable: false },
      { field: 'name', title: '项目名称', width: 200, editable: false },
      { field: 'projectAttr', title: '项目特征', width: 180, editable: false },
      { field: 'unit', title: '单位', width: 70, editable: false },
      { field: 'quantity', title: '申报工程量', width: 120, dataType: 'number', editable: false },
      { field: 'auditQuantity', title: '审核工程量', width: 120, dataType: 'number' },
      { field: 'settlementUnitPrice', title: '申报单价', width: 120, dataType: 'number', editable: false },
      { field: 'auditUnitPrice', title: '审核单价', width: 120, dataType: 'number' },
      { field: 'settlementAmount', title: '申报合价', width: 150, dataType: 'number', editable: false },
      { field: 'auditAmount', title: '审核合价', width: 150, dataType: 'number', editable: false },
      { field: 'auditOpinion', title: '审核意见', width: 200, component: 'textarea' },
      { field: 'auditStatus', title: '审核状态', width: 100, component: 'select',
        options: [
          { value: 'approved', label: '通过' },
          { value: 'rejected', label: '不通过' },
          { value: 'modified', label: '调整' }
        ]
      }
    ]
  }
}

// 右键菜单配置生成器
export const generateMenuConfig = (moduleType) => {
  const config = MODULE_CONFIGS[moduleType]
  const baseMenu = {
    options: [
      {
        code: 'add',
        name: '插入',
        children: [
          { code: 0, name: '添加分部', kind: '01' },
          { code: 1, name: '添加子分部', kind: '02' }
        ]
      },
      { code: 'copy', name: '复制' },
      { code: 'paste', name: '粘贴' }
    ]
  }

  // 根据模块类型添加特定菜单项
  if (moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi') {
    // 概算/工料机：添加子目
    baseMenu.options[0].children.push({ code: 2, name: '添加子目', kind: '-1' })
    
    if (moduleType === 'gaiSuan') {
      baseMenu.options.push({ code: 'copyCell', name: '复制单元格内容' })
      baseMenu.options.push({ code: 'pasteChild', name: '粘贴为子项' })
    }
  } else {
    // 预算/结算/审核：添加清单和子目
    baseMenu.options[0].children.push({ code: 2, name: '添加清单', kind: '03' })
    baseMenu.options[0].children.push({ code: 3, name: '添加子目', kind: '04' })
    
    baseMenu.options.push({ code: 'delete', name: '删除' })
    baseMenu.options.push({ code: 'lock', name: '清单锁定' })
    baseMenu.options.push({ code: 'pageColumnSetting', name: '页面显示列设置' })
  }

  return baseMenu
}

// 编辑条件判断器
export const createEditableChecker = (moduleType) => {
  const config = MODULE_CONFIGS[moduleType]
  
  return {
    // 编码编辑条件
    codeEditable: (record) => {
      if (moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi') {
        return record?.kind !== '07'
      } else {
        return true // 预算/结算/审核默认可编辑
      }
    },
    
    // 类型编辑条件
    typeEditable: (record) => {
      if (moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi') {
        return record.isTempRemove !== 1 && 
               record?.kind !== '07' &&
               (record.deResourceKind === 2 || record.deResourceKind === 5 || record.deResourceKind === 4)
      } else {
        return record.typeList?.map(item => item.desc).includes(record.type)
      }
    },
    
    // 项目特征编辑条件
    projectAttrEditable: (record) => {
      if (config.features.hasProjectAttr) {
        return ['03'].includes(record.kind)
      }
      return false
    }
  }
}

// 获取模块配置
export const getModuleConfig = (moduleType) => {
  return MODULE_CONFIGS[moduleType] || MODULE_CONFIGS.yuSuan
}

// 验证模块类型
export const isValidModuleType = (moduleType) => {
  return Object.keys(MODULE_CONFIGS).includes(moduleType)
}
