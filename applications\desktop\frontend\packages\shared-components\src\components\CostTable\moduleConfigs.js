/**
 * CostTable 模块配置管理
 * 统一管理概预结审工料机五个模块的差异配置
 */

// 模块基础配置
export const MODULE_CONFIGS = {
  // 概算模块
  gaiSuan: {
    name: '概算',
    codeField: 'deCode',
    nameField: 'deName',
    apiModule: 'gSdetailApi',
    hooks: 'useGsSubItem',
    color: '#1890ff',
    features: {
      hasCheckbox: false,
      hasProjectAttr: false,
      hasSpecification: true,
      hasQuantityExpression: true,
      hasResqtyExp: true,
      supportsTempRemove: false,
      supportsLock: true,
      supportsIntroductionQuantity: true,
      supportsBatchModifyMainMaterials: true
    }
  },
  
  // 预算模块
  yuSuan: {
    name: '预算',
    codeField: 'bdCode',
    nameField: 'name',
    apiModule: 'detailApi',
    hooks: 'useSubItemStable',
    color: '#52c41a',
    features: {
      hasCheckbox: true,
      hasProjectAttr: true,
      hasSpecification: true,
      hasQuantityExpression: false,
      hasResqtyExp: false,
      supportsTempRemove: false,
      supportsLock: true,
      supportsCodeReset: true,
      supportsSyncNameToDE: true
    }
  },
  
  // 结算模块
  jieSuan: {
    name: '结算',
    codeField: 'bdCode',
    nameField: 'name',
    apiModule: 'detailApi',
    hooks: 'useSubItemStable',
    color: '#722ed1',
    features: {
      hasCheckbox: true,
      hasProjectAttr: true,
      hasSpecification: true,
      hasQuantityExpression: false,
      hasResqtyExp: false,
      supportsTempRemove: false,
      supportsLock: true,
      supportsContractAssociation: true,
      supportsProgressSettlement: true
    }
  },
  
  // 审核模块
  shenHe: {
    name: '审核',
    codeField: 'bdCode',
    nameField: 'name',
    apiModule: 'shApi',
    hooks: 'useSubItemStable',
    color: '#faad14',
    features: {
      hasCheckbox: true,
      hasProjectAttr: true,
      hasSpecification: true,
      hasQuantityExpression: false,
      hasResqtyExp: false,
      supportsTempRemove: true,
      supportsLock: true,
      supportsAuditComparison: true,
      supportsAuditOpinion: true
    }
  },
  
  // 工料机模块
  gongLiaoJi: {
    name: '工料机',
    codeField: 'deCode',
    nameField: 'deName',
    apiModule: 'gljdetailApi',
    hooks: 'useGljSubItem',
    color: '#eb2f96',
    features: {
      hasCheckbox: false,
      hasProjectAttr: false,
      hasSpecification: true,
      hasQuantityExpression: false,
      hasResqtyExp: true,
      supportsTempRemove: false,
      supportsLock: true,
      supportsStandardConversion: true,
      supportsSetMainMaterial: true,
      supportsProjectAttrAssociation: true
    }
  }
}

// 列配置生成器
export const generateColumnConfig = (moduleType) => {
  const config = MODULE_CONFIGS[moduleType]
  if (!config) {
    throw new Error(`Unknown module type: ${moduleType}`)
  }

  const baseColumns = []

  // 复选框列（预算/结算/审核）
  if (config.features.hasCheckbox) {
    baseColumns.push({
      field: 'selection',
      title: '',
      width: 40,
      fixed: 'left',
      type: 'checkbox',
      resizable: false
    })
  }

  // 序号列
  baseColumns.push({
    field: 'dispNo',
    title: '序号',
    width: config.features.hasCheckbox ? 40 : 50,
    fixed: 'left',
    resizable: false
  })

  // 编码列（树形结构）
  baseColumns.push({
    field: config.codeField,
    title: moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi' ? '定额编码' : '项目编码',
    width: moduleType === 'gaiSuan' ? 190 : 170,
    editable: true,
    treeNode: true
  })

  // 类别/类型列
  if (moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi') {
    baseColumns.push({
      field: 'type',
      title: '类别',
      width: 65,
      editable: true
    })
  } else {
    baseColumns.push({
      field: 'type',
      title: '类型',
      width: 60,
      editable: true
    })
  }

  // 名称列
  baseColumns.push({
    field: config.nameField,
    title: moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi' ? '定额名称' : '项目名称',
    width: moduleType === 'gaiSuan' ? 260 : 200,
    editable: true
  })

  // 项目特征列（预算/结算/审核）
  if (config.features.hasProjectAttr) {
    baseColumns.push({
      field: 'projectAttr',
      title: '项目特征',
      width: 180,
      editable: true
    })
  }

  // 规格型号列
  if (config.features.hasSpecification) {
    baseColumns.push({
      field: 'specification',
      title: '规格型号',
      width: 80,
      editable: true,
      hidden: moduleType === 'gaiSuan' // 概算模块默认隐藏
    })
  }

  // 单位列
  baseColumns.push({
    field: 'unit',
    title: '单位',
    width: 70,
    editable: true
  })

  // 消耗量列（概算/工料机）
  if (config.features.hasResqtyExp) {
    baseColumns.push({
      field: 'resqtyExp',
      title: '消耗量',
      width: 100,
      editable: true
    })
  }

  // 工程量表达式列（概算）
  if (config.features.hasQuantityExpression) {
    baseColumns.push({
      field: 'quantityExpression',
      title: '工程量表达式',
      width: 120,
      editable: true
    })
  }

  return baseColumns
}

// 右键菜单配置生成器
export const generateMenuConfig = (moduleType) => {
  const config = MODULE_CONFIGS[moduleType]
  const baseMenu = {
    options: [
      {
        code: 'add',
        name: '插入',
        children: [
          { code: 0, name: '添加分部', kind: '01' },
          { code: 1, name: '添加子分部', kind: '02' }
        ]
      },
      { code: 'copy', name: '复制' },
      { code: 'paste', name: '粘贴' }
    ]
  }

  // 根据模块类型添加特定菜单项
  if (moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi') {
    // 概算/工料机：添加子目
    baseMenu.options[0].children.push({ code: 2, name: '添加子目', kind: '-1' })
    
    if (moduleType === 'gaiSuan') {
      baseMenu.options.push({ code: 'copyCell', name: '复制单元格内容' })
      baseMenu.options.push({ code: 'pasteChild', name: '粘贴为子项' })
    }
  } else {
    // 预算/结算/审核：添加清单和子目
    baseMenu.options[0].children.push({ code: 2, name: '添加清单', kind: '03' })
    baseMenu.options[0].children.push({ code: 3, name: '添加子目', kind: '04' })
    
    baseMenu.options.push({ code: 'delete', name: '删除' })
    baseMenu.options.push({ code: 'lock', name: '清单锁定' })
    baseMenu.options.push({ code: 'pageColumnSetting', name: '页面显示列设置' })
  }

  return baseMenu
}

// 编辑条件判断器
export const createEditableChecker = (moduleType) => {
  const config = MODULE_CONFIGS[moduleType]
  
  return {
    // 编码编辑条件
    codeEditable: (record) => {
      if (moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi') {
        return record?.kind !== '07'
      } else {
        return true // 预算/结算/审核默认可编辑
      }
    },
    
    // 类型编辑条件
    typeEditable: (record) => {
      if (moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi') {
        return record.isTempRemove !== 1 && 
               record?.kind !== '07' &&
               (record.deResourceKind === 2 || record.deResourceKind === 5 || record.deResourceKind === 4)
      } else {
        return record.typeList?.map(item => item.desc).includes(record.type)
      }
    },
    
    // 项目特征编辑条件
    projectAttrEditable: (record) => {
      if (config.features.hasProjectAttr) {
        return ['03'].includes(record.kind)
      }
      return false
    }
  }
}

// 获取模块配置
export const getModuleConfig = (moduleType) => {
  return MODULE_CONFIGS[moduleType] || MODULE_CONFIGS.yuSuan
}

// 验证模块类型
export const isValidModuleType = (moduleType) => {
  return Object.keys(MODULE_CONFIGS).includes(moduleType)
}
