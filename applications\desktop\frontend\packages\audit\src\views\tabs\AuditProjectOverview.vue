<template>
  <div class="audit-project-overview">
    <!-- 使用新的ProjectOverview共享组件 -->
    <ProjectOverview
      module-type="review"
      :level-type="levelType"
      :project-data="projectData"
      :tab-info="tabInfo"
      :editable="editable"
      :custom-config="customConfig"
      :permissions="permissions"
      @data-change="handleDataChange"
      @save="handleSave"
      @export="handleExport"
      @lock="handleLock"
      @action="handleAction"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ProjectOverview } from '@cost-app/shared-components'

const props = defineProps({
  levelType: {
    type: Number,
    default: 3
  },
  tabInfo: {
    type: Object,
    default: () => ({ code: 'project-overview', name: '项目概况' })
  },
  projectId: {
    type: String,
    default: ''
  },
  unitId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 响应式数据
const editable = ref(true)
const projectData = ref({
  projectName: '',
  projectCode: '',
  buildingArea: null,
  projectScale: null,
  constructionUnit: '',
  designUnit: '',
  compileDate: '',
  compiler: '',
  reviewer: '',
  reviewDate: '',
  // 审核特有字段
  reviewType: '',
  auditOpinion: '',
  auditResult: ''
})

// 自定义配置
const customConfig = computed(() => ({
  // 审核模块特定配置
  hiddenFields: [], // 隐藏的字段
  hideEngineerFeature: true, // 审核模块不显示工程特征
  validationRules: {
    projectName: [
      { required: true, message: '项目名称不能为空' }
    ],
    auditResult: [
      { required: true, message: '审核结果不能为空' }
    ]
  },
  // 审核特有字段配置
  extraFields: {
    reviewType: {
      name: '审核类型',
      type: 'select',
      required: true,
      options: [
        { label: '预算审核', value: 'budget' },
        { label: '结算审核', value: 'settlement' },
        { label: '变更审核', value: 'change' }
      ]
    },
    auditOpinion: {
      name: '审核意见',
      type: 'textarea',
      required: false,
      maxLength: 500,
      rows: 3
    },
    auditResult: {
      name: '审核结果',
      type: 'select',
      required: true,
      options: [
        { label: '通过', value: 'approved' },
        { label: '不通过', value: 'rejected' },
        { label: '需修改', value: 'modified' }
      ]
    }
  }
}))

// 权限配置
const permissions = computed(() => ({
  canEdit: true,
  canDelete: false, // 审核记录不可删除
  canLock: true,
  canExport: true,
  canImport: false
}))

// 事件处理
const handleDataChange = (data) => {
  console.log('审核概况数据变更:', data)
  
  // 更新本地数据
  if (data.type === 'basic') {
    updateProjectData(data.data)
  }
  
  emit('dataChange', data)
}

const handleSave = (data) => {
  console.log('保存审核概况:', data)
  
  // 审核模块特有的保存逻辑
  const saveData = {
    ...data,
    moduleType: 'review',
    saveTime: new Date().toISOString()
  }
  
  // 调用审核API保存
  saveAuditOverview(saveData)
  
  emit('action', 'save', saveData)
}

const handleExport = (data) => {
  console.log('导出审核概况:', data)
  
  // 审核模块特有的导出逻辑
  const exportData = {
    ...data,
    exportType: 'audit-overview',
    exportTime: new Date().toISOString()
  }
  
  // 调用审核API导出
  exportAuditOverview(exportData)
  
  emit('action', 'export', exportData)
}

const handleLock = (lockStatus) => {
  console.log('锁定状态变更:', lockStatus)
  
  // 审核模块特有的锁定逻辑
  updateLockStatus(lockStatus)
  
  emit('action', 'lock', lockStatus)
}

const handleAction = (action, data) => {
  console.log('审核概况操作:', action, data)
  emit('action', action, data)
}

// 业务逻辑函数
const updateProjectData = (data) => {
  Object.assign(projectData.value, data)
}

const saveAuditOverview = async (data) => {
  try {
    // 调用审核API保存概况信息
    console.log('保存审核概况成功:', data)
    message.success('审核概况保存成功')
  } catch (error) {
    console.error('保存审核概况失败:', error)
    message.error('保存失败')
  }
}

const exportAuditOverview = async (data) => {
  try {
    // 调用审核API导出概况信息
    console.log('导出审核概况成功:', data)
    message.success('审核概况导出成功')
  } catch (error) {
    console.error('导出审核概况失败:', error)
    message.error('导出失败')
  }
}

const updateLockStatus = async (lockStatus) => {
  try {
    // 调用审核API更新锁定状态
    console.log('更新锁定状态成功:', lockStatus)
    message.success(lockStatus ? '锁定成功' : '解锁成功')
  } catch (error) {
    console.error('更新锁定状态失败:', error)
    message.error('操作失败')
  }
}

// 数据加载函数
const loadProjectData = async () => {
  if (!props.projectId) return

  try {
    // 加载项目基本数据
    // const data = await auditApi.getProjectOverview(props.projectId)
    // Object.assign(projectData.value, data)
    console.log('加载项目数据:', props.projectId)
  } catch (error) {
    console.error('加载项目数据失败:', error)
    message.error('加载数据失败')
  }
}

// 监听器
watch(() => props.projectId, () => {
  // 项目ID变化时重新加载数据
  loadProjectData()
}, { immediate: true })

// 生命周期
onMounted(() => {
  console.log('审核概况组件已挂载')
  loadProjectData()
})
</script>

<style lang="scss" scoped>
.audit-project-overview {
  height: 100%;
  
  // 审核模块特有样式
  :deep(.project-overview) {
    .page-header h3 {
      color: #722ed1; // 审核模块主色调
    }
    
    .ant-tabs-tab {
      &.ant-tabs-tab-active {
        color: #722ed1;
      }
    }
    
    .field-highlight {
      color: #722ed1;
    }
  }
}
</style>
