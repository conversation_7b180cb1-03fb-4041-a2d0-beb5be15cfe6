# TabMenu 组件文档

## 概述

TabMenu 是一个智能的多模块标签页导航组件，基于 tabmenu.md 规范设计，支持预算、概算、结算、工料机四种模块，以及项目、单项、单位三种层级的动态菜单展示。

## 特性

- 🎯 **智能过滤**：根据模块类型和层级自动过滤可用标签
- 🔄 **模块切换**：支持预算/概算/结算/工料机四种模块
- 📊 **层级支持**：适配工程项目/单项工程/单位工程三个层级
- 🔒 **权限控制**：支持专业工程权限和一级子单项限制
- 🎨 **主题色彩**：每个模块有独特的主题色彩
- 📱 **响应式**：支持不同屏幕尺寸自适应

## 安装使用

```javascript
import { TabMenu } from '@cost-app/shared-components'
```

## 基础用法

```vue
<template>
  <TabMenu
    :level-type="3"
    module-type="budget"
    @tab-change="handleTabChange"
  />
</template>

<script setup>
const handleTabChange = (tabData) => {
  console.log('当前Tab:', tabData)
  // { code: '4', value: '分部分项', levelType: 3, moduleType: 'budget' }
}
</script>
```

## Props 属性

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `levelType` | `Number` | ✅ | - | 层级类型：1-工程项目，2-单项工程，3-单位工程 |
| `moduleType` | `String` | ✅ | - | 模块类型：`budget`/`rough-estimate`/`settlement`/`material` |
| `isFirstLevelChild` | `Boolean` | ❌ | `true` | 是否为一级子单项（影响菜单显示） |
| `isStandardGroup` | `Boolean` | ❌ | `false` | 标准组模式（单位工程简化显示） |
| `hasProfessionalProject` | `Boolean` | ❌ | `true` | 是否有专业工程设置 |
| `defaultActiveKey` | `String` | ❌ | `'1'` | 初始激活的tab key |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `tabChange` | `{ code, value, levelType, moduleType }` | Tab切换时触发 |
| `getTitle` | `String` | Tab标题变化时触发 |

## Methods 方法

通过 `ref` 调用组件方法：

```vue
<template>
  <TabMenu ref="tabMenuRef" />
  <button @click="switchTab">切换到分部分项</button>
</template>

<script setup>
import { ref } from 'vue'

const tabMenuRef = ref()

const switchTab = () => {
  // 根据名称切换Tab
  tabMenuRef.value.changeTabByName('分部分项')

  // 获取当前Tab信息
  const currentTab = tabMenuRef.value.getCurrentTab()
  console.log(currentTab)
}
</script>
```

### changeTabByName(tabName: string): boolean

根据Tab名称切换到指定Tab

- **参数**：`tabName` - Tab名称（如："分部分项"、"工程概况"等）
- **返回值**：`boolean` - 切换是否成功

### getCurrentTab(): object | undefined

获取当前激活的Tab信息

- **返回值**：`{ code: string, value: string }` 或 `undefined`

### filteredTabs: array (只读)

获取当前过滤后的Tab列表

## 模块菜单配置

### 预算模块 (budget)
功能最全面的标准模式，支持完整的预算编制流程。

#### 工程项目层级 (levelType: 1)
- ✅ 项目概况
- ✅ 造价分析
- ✅ 取费表
- ✅ 人材机汇总

#### 单项工程层级 (levelType: 2)
- ✅ 造价分析
- ✅ 人材机汇总

#### 单位工程层级 (levelType: 3)
- ✅ 工程概况
- ✅ 造价分析
- ✅ 取费表
- ✅ 分部分项
- ✅ 措施项目
- ✅ 人材机汇总
- ✅ 其他项目
- ✅ 费用汇总

### 概算模块 (rough-estimate)
增加概算特有功能，适用于项目初期估算。

#### 工程项目层级 (levelType: 1)
- ✅ 项目概况
- ✅ 造价分析
- ✅ 取费表
- ✅ 人材机汇总
- ✅ 设备购置费
- ✅ 建设其他费
- ✅ 概算汇总
- ✅ 调整概算

#### 单项工程层级 (levelType: 2)
- ✅ 造价分析
- ✅ 人材机汇总

#### 单位工程层级 (levelType: 3)
- ✅ 工程概况
- ✅ 造价分析
- ✅ 取费表
- ✅ 预算书（替代分部分项）
- ✅ 人材机汇总
- ✅ 费用汇总
- ✅ 独立费

### 结算模块 (settlement)
简化菜单，专注结算审核，人材机改为调整模式。

#### 工程项目层级 (levelType: 1)
- ✅ 项目概况
- ✅ 造价分析
- ✅ 人材机调整（重命名）

#### 单项工程层级 (levelType: 2)
- ✅ 造价分析

#### 单位工程层级 (levelType: 3)
- ✅ 项目概况（重命名）
- ✅ 分部分项
- ✅ 措施项目
- ✅ 人材机调整（重命名）
- ✅ 其他项目
- ✅ 费用汇总

### 工料机模块 (material)
专注预算书和独立费管理。

#### 工程项目层级 (levelType: 1)
- ✅ 项目概况
- ✅ 造价分析
- ✅ 取费表
- ✅ 人材机汇总

#### 单项工程层级 (levelType: 2)
- ✅ 造价分析
- ✅ 取费表
- ✅ 人材机汇总

#### 单位工程层级 (levelType: 3)
- ✅ 工程概况
- ✅ 造价分析
- ✅ 取费表
- ✅ 预算书（替代分部分项）
- ✅ 措施项目
- ✅ 人材机汇总
- ✅ 其他项目
- ✅ 费用汇总
- ✅ 独立费

## 特殊逻辑处理

### 权限控制

1. **非一级子单项限制**
   - 当 `isFirstLevelChild: false` 且 `levelType: 2` 时
   - 过滤掉：人材机汇总、人材机调整、取费表

2. **专业工程权限**
   - 当 `hasProfessionalProject: false` 时
   - 工程项目层级：只显示"项目概况"
   - 单位工程层级：只显示"分部分项"

### 标准组模式

当 `isStandardGroup: true` 且 `levelType: 3` 时，单位工程只显示：
- 分部分项
- 措施项目
- 人材机汇总

## 样式主题

组件支持四种模块主题，通过添加对应的CSS类启用：

```vue
<TabMenu
  module-type="budget"
  :class="`${moduleType}-theme`"
/>
```

### 主题色彩

- **预算模块** (`budget-theme`)：绿色主题 `#52c41a`
- **概算模块** (`rough-estimate-theme`)：蓝色主题 `#1890ff`
- **结算模块** (`settlement-theme`)：紫色主题 `#722ed1`
- **工料机模块** (`material-theme`)：橙色主题 `#faad14`

## 完整示例

```vue
<template>
  <div class="project-detail">
    <!-- 项目信息 -->
    <div class="project-info">
      <h2>{{ projectName }}</h2>
      <p>当前层级: {{ levelNames[currentLevel] }}</p>
      <p>当前模块: {{ moduleNames[currentModule] }}</p>
    </div>

    <!-- Tab导航 -->
    <TabMenu
      ref="tabMenuRef"
      :level-type="currentLevel"
      :module-type="currentModule"
      :is-first-level-child="isFirstLevel"
      :is-standard-group="isStandardGroup"
      :has-professional-project="hasProfessional"
      :class="`${currentModule}-theme`"
      @tab-change="handleTabChange"
      @get-title="handleTitleChange"
    />

    <!-- 内容区域 -->
    <div class="tab-content">
      <component :is="currentTabComponent" />
    </div>

    <!-- 操作按钮 -->
    <div class="actions">
      <a-button @click="switchToNextModule">切换模块</a-button>
      <a-button @click="switchToNextLevel">切换层级</a-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { TabMenu } from '@cost-app/shared-components'

const tabMenuRef = ref()
const currentLevel = ref(3)
const currentModule = ref('budget')
const currentTabInfo = ref({})

const projectName = ref('办公楼建设项目')
const isFirstLevel = ref(true)
const isStandardGroup = ref(false)
const hasProfessional = ref(true)

const moduleNames = {
  'budget': '预算模块',
  'rough-estimate': '概算模块',
  'settlement': '结算模块',
  'material': '工料机模块'
}

const levelNames = {
  1: '工程项目层级',
  2: '单项工程层级',
  3: '单位工程层级'
}

const currentTabComponent = computed(() => {
  // 根据当前Tab动态加载对应组件
  const tabCode = currentTabInfo.value.code
  return `Tab${tabCode}Component`
})

const handleTabChange = (tabData) => {
  currentTabInfo.value = tabData
  console.log('Tab切换:', tabData)
}

const handleTitleChange = (title) => {
  document.title = `${title} - ${projectName.value}`
}

const switchToNextModule = () => {
  const modules = ['budget', 'rough-estimate', 'settlement', 'material']
  const currentIndex = modules.indexOf(currentModule.value)
  currentModule.value = modules[(currentIndex + 1) % modules.length]
}

const switchToNextLevel = () => {
  const levels = [1, 2, 3]
  const currentIndex = levels.indexOf(currentLevel.value)
  currentLevel.value = levels[(currentIndex + 1) % levels.length]
}
</script>

<style lang="scss" scoped>
.project-detail {
  padding: 24px;

  .project-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f5f5f5;
    border-radius: 6px;
  }

  .tab-content {
    min-height: 400px;
    padding: 24px;
    border: 1px solid #d9d9d9;
    border-top: none;
    background: white;
  }

  .actions {
    margin-top: 16px;
    text-align: center;
  }
}

// 引入模块主题样式
:deep(.budget-theme .ant-tabs-tab-active) {
  background-color: #f6ffed;
  border-bottom: 2px solid #52c41a;
  .ant-tabs-tab-btn { color: #52c41a; }
}

:deep(.rough-estimate-theme .ant-tabs-tab-active) {
  background-color: #e6f7ff;
  border-bottom: 2px solid #1890ff;
  .ant-tabs-tab-btn { color: #1890ff; }
}

:deep(.settlement-theme .ant-tabs-tab-active) {
  background-color: #f9f0ff;
  border-bottom: 2px solid #722ed1;
  .ant-tabs-tab-btn { color: #722ed1; }
}

:deep(.material-theme .ant-tabs-tab-active) {
  background-color: #fff7e6;
  border-bottom: 2px solid #faad14;
  .ant-tabs-tab-btn { color: #faad14; }
}
</style>
```

## 注意事项

1. **层级类型验证**：`levelType` 必须为 1、2、3 中的一个
2. **模块类型验证**：`moduleType` 必须为支持的四种模块之一
3. **Tab切换限制**：某些Tab可能因权限限制而不可用
4. **响应式设计**：组件会根据容器宽度自动调整
5. **主题一致性**：建议在同一模块内保持主题色彩一致

## 版本历史

- **v1.0.0** (2024-09-24)
  - 初始版本发布
  - 支持四种模块和三种层级
  - 实现基础的过滤和权限控制逻辑