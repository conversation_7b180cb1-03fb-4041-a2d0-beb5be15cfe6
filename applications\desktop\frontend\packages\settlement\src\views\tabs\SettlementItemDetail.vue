<template>
  <div class="settlement-item-detail">
    <!-- 使用配置化的ItemDetail组件 - 结算模块 -->
    <ConfigurableItemDetail
      module-type="jieSuan"
      :table-data="tableData"
      :custom-field-config="settlementFieldConfig"
      :edit-conditions="settlementEditConditions"
      :project-data="projectData"
      :tab-info="tabInfo"
      :level-type="levelType"
      @data-change="handleDataChange"
      @action="handleAction"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { ConfigurableItemDetail } from '@cost-app/shared-components'

const props = defineProps({
  moduleType: String,
  levelType: Number,
  tabInfo: Object,
  projectData: Object,
  tableData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 结算模块特有的字段配置
const settlementFieldConfig = ref({
  // 项目编码和名称在结算中不可编辑
  bdCode: {
    editable: false,
    title: '项目编码',
    componentProps: { 
      disabled: true,
      style: { backgroundColor: '#f5f5f5' }
    }
  },
  name: {
    editable: false,
    title: '项目名称',
    componentProps: { 
      disabled: true,
      style: { backgroundColor: '#f5f5f5' }
    }
  },
  
  // 合同工程量不可编辑
  contractQuantity: {
    editable: false,
    title: '合同工程量',
    component: 'number',
    componentProps: { 
      precision: 3, 
      disabled: true,
      style: { backgroundColor: '#f5f5f5' }
    },
    formatter: (value) => `${Number(value || 0).toFixed(3)}`
  },
  
  // 实际工程量根据合同类型决定是否可编辑
  quantity: {
    title: '实际工程量',
    component: 'number',
    componentProps: { 
      precision: 3, 
      min: 0,
      placeholder: '请输入实际工程量'
    }
  },
  
  // 合同单价不可编辑
  contractUnitPrice: {
    editable: false,
    title: '合同单价',
    component: 'number',
    componentProps: { 
      precision: 2, 
      disabled: true,
      style: { backgroundColor: '#f5f5f5' }
    },
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  },
  
  // 结算单价可编辑
  settlementUnitPrice: {
    title: '结算单价',
    component: 'number',
    componentProps: { 
      precision: 2, 
      min: 0,
      placeholder: '请输入结算单价'
    },
    required: true
  },
  
  // 结算合价自动计算，不可编辑
  settlementAmount: {
    editable: false,
    title: '结算合价',
    component: 'number',
    componentProps: { 
      precision: 2, 
      disabled: true,
      style: { backgroundColor: '#f0f8ff' }
    },
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  },
  
  // 合同类型选择
  contractType: {
    title: '合同类型',
    component: 'select',
    componentProps: {
      placeholder: '请选择合同类型'
    },
    options: [
      { value: 'within', label: '合同内', color: '#52c41a' },
      { value: 'external', label: '合同外', color: '#faad14' },
      { value: 'change', label: '变更', color: '#1890ff' }
    ],
    required: true
  }
})

// 结算模块的编辑条件
const settlementEditConditions = ref({
  // 实际工程量编辑条件：只有合同外和变更项目可以编辑工程量
  quantity: (context) => {
    const { record } = context
    return record.contractType === 'external' || record.contractType === 'change'
  },
  
  // 结算单价编辑条件：根据项目状态和合同类型决定
  settlementUnitPrice: (context) => {
    const { record } = context
    // 已锁定或已审核的项目不可编辑
    if (record.status === 'locked' || record.status === 'approved') {
      return false
    }
    // 合同内项目的单价变动需要特殊权限
    if (record.contractType === 'within' && !record.allowPriceChange) {
      return false
    }
    return true
  },
  
  // 合同类型编辑条件：项目创建后一般不允许修改合同类型
  contractType: (context) => {
    const { record } = context
    return record.status === 'draft' || record.allowTypeChange === true
  }
})

// 事件处理
const handleDataChange = (newData) => {
  emit('dataChange', newData)
}

const handleAction = (actionType, actionData) => {
  console.log('结算模块操作:', actionType, actionData)
  
  switch (actionType) {
    case 'cellEdited':
      handleCellEdited(actionData)
      break
    case 'addRow':
      message.success('新增结算项目成功')
      break
    case 'deleteRow':
      message.success('删除结算项目成功')
      break
    case 'contextMenu':
      handleContextMenu(actionData)
      break
    default:
      // 其他操作直接传递给父组件
      emit('action', actionType, actionData)
  }
}

const handleCellEdited = ({ record, column, newValue, oldValue }) => {
  // 结算模块特有的计算逻辑
  if (column.field === 'quantity' || column.field === 'settlementUnitPrice') {
    // 自动计算结算合价
    record.settlementAmount = (record.quantity || 0) * (record.settlementUnitPrice || 0)
    
    // 如果是合同外或变更项目，需要特殊标记
    if (record.contractType === 'external' || record.contractType === 'change') {
      record.isExtraWork = true
    }
  }
  
  // 合同类型变更时的处理
  if (column.field === 'contractType') {
    if (newValue === 'within') {
      // 合同内项目，工程量和单价应该参考合同值
      record.quantity = record.contractQuantity || record.quantity
      record.settlementUnitPrice = record.contractUnitPrice || record.settlementUnitPrice
    }
    message.info(`项目类型已变更为: ${getContractTypeLabel(newValue)}`)
  }
  
  message.success('结算数据更新成功')
}

const getContractTypeLabel = (type) => {
  const typeMap = {
    'within': '合同内',
    'external': '合同外', 
    'change': '变更'
  }
  return typeMap[type] || type
}

const handleContextMenu = ({ key, record }) => {
  switch (key) {
    case 'copy':
      message.success(`复制结算项目: ${record.name}`)
      break
    case 'paste':
      message.success('粘贴结算项目')
      break
    case 'delete':
      message.success(`删除结算项目: ${record.name}`)
      break
    case 'addSub':
      message.success(`为 ${record.name} 添加子项目`)
      break
    case 'viewDetail':
      message.info(`查看结算详情: ${record.name}`)
      break
    case 'exportItem':
      message.success(`导出结算项目: ${record.name}`)
      break
    case 'lockItem':
      record.status = 'locked'
      message.success(`锁定项目: ${record.name}`)
      break
    case 'unlockItem':
      record.status = 'draft'
      message.success(`解锁项目: ${record.name}`)
      break
    default:
      console.log('未处理的结算右键菜单操作:', key, record)
  }
}
</script>

<style lang="scss" scoped>
.settlement-item-detail {
  height: 100%;
  overflow: hidden;
  
  // 结算模块特有样式
  :deep(.ant-table-tbody > tr > td) {
    // 合同内项目的特殊样式
    &.contract-within {
      background-color: #f6ffed;
    }
    
    // 合同外项目的特殊样式
    &.contract-external {
      background-color: #fff7e6;
    }
    
    // 变更项目的特殊样式
    &.contract-change {
      background-color: #e6f7ff;
    }
    
    // 锁定项目的样式
    &.status-locked {
      background-color: #f5f5f5;
      color: #999;
    }
  }
}
</style>
