<template>
  <div class="tab-content-enhanced">
    <component
      :is="currentComponent"
      v-if="currentComponent"
      v-bind="computedProps"
      @data-change="handleDataChange"
      @action="handleAction"
    >
      <!-- 允许父组件传递自定义插槽内容 -->
      <template v-for="slot in Object.keys($slots)" :key="slot" #[slot]="slotProps">
        <slot :name="slot" v-bind="slotProps" />
      </template>
    </component>

    <!-- 无匹配组件时的占位 -->
    <div v-else class="no-content">
      <a-empty
        :description="`${currentTab?.value || '当前Tab'} 页面开发中...`"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, defineAsyncComponent } from 'vue'
import { Empty } from 'ant-design-vue'
import { useTabStrategy } from '../composables/useTabStrategy'
import { useTabConfig } from '../composables/useTabConfig'

const props = defineProps({
  // 当前活跃的Tab信息
  currentTab: {
    type: Object,
    default: () => ({})
  },
  // 模块类型
  moduleType: {
    type: String,
    required: true,
    validator: (value) => ['budget', 'rough-estimate', 'settlement', 'material'].includes(value)
  },
  // 层级类型
  levelType: {
    type: Number,
    required: true,
    validator: (value) => [1, 2, 3].includes(value)
  },
  // 项目数据
  projectData: {
    type: Object,
    default: () => ({})
  },
  // 表格数据
  tableData: {
    type: Array,
    default: () => []
  },
  // 权限配置
  isFirstLevelChild: {
    type: Boolean,
    default: true
  },
  isStandardGroup: {
    type: Boolean,
    default: false
  },
  hasProfessionalProject: {
    type: Boolean,
    default: true
  },
  // 业务配置扩展
  businessConfig: {
    type: Object,
    default: () => ({})
  },
  // 额外的组件属性
  componentProps: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['dataChange', 'action', 'componentLoad'])

// 使用Tab策略和配置hooks
const { getTabStrategy } = useTabStrategy()
const { getTabConfig } = useTabConfig()

// 获取当前Tab的业务策略
const currentStrategy = computed(() => {
  return getTabStrategy({
    tabCode: props.currentTab?.code,
    moduleType: props.moduleType,
    levelType: props.levelType,
    isFirstLevelChild: props.isFirstLevelChild,
    isStandardGroup: props.isStandardGroup,
    hasProfessionalProject: props.hasProfessionalProject
  })
})

// 获取当前Tab的配置
const currentConfig = computed(() => {
  return getTabConfig({
    tabCode: props.currentTab?.code,
    moduleType: props.moduleType,
    levelType: props.levelType,
    strategy: currentStrategy.value
  })
})

// 动态组件名称
const componentName = computed(() => {
  const strategy = currentStrategy.value
  if (!strategy) return null

  return strategy.getComponentName({
    tabCode: props.currentTab?.code,
    moduleType: props.moduleType,
    levelType: props.levelType
  })
})

// 异步组件加载器
const createAsyncComponent = (componentName) => {
  return defineAsyncComponent({
    loader: () => {
      try {
        return import(`./tab-pages/${componentName}.vue`)
      } catch (error) {
        console.warn(`组件 ${componentName} 未找到，尝试加载通用组件`)
        return import(`./tab-pages/GenericTabPage.vue`).catch(() => {
          console.error(`通用组件加载失败`)
          return Promise.resolve({
            template: `
              <div class="component-placeholder">
                <a-result
                  status="info"
                  :title="'${componentName} 组件'"
                  sub-title="该功能页面正在开发中..."
                />
              </div>
            `
          })
        })
      }
    },
    loadingComponent: {
      template: `
        <div class="component-loading">
          <a-spin size="large" tip="加载中...">
            <div style="height: 200px;"></div>
          </a-spin>
        </div>
      `
    },
    errorComponent: {
      template: `
        <div class="component-error">
          <a-result
            status="error"
            title="组件加载失败"
            sub-title="请检查网络连接或刷新页面重试"
          />
        </div>
      `
    },
    delay: 200,
    timeout: 10000
  })
}

// 当前组件实例
const currentComponent = computed(() => {
  const name = componentName.value
  if (!name) return null
  return createAsyncComponent(name)
})

// 计算传递给子组件的属性
const computedProps = computed(() => {
  const config = currentConfig.value
  const strategy = currentStrategy.value

  if (!config || !strategy) {
    return {
      moduleType: props.moduleType,
      levelType: props.levelType,
      tabInfo: props.currentTab,
      projectData: props.projectData,
      tableData: props.tableData,
      ...props.componentProps
    }
  }

  // 基础属性
  const baseProps = {
    moduleType: props.moduleType,
    levelType: props.levelType,
    tabInfo: props.currentTab,
    projectData: props.projectData,
    tableData: props.tableData,
    isFirstLevelChild: props.isFirstLevelChild,
    isStandardGroup: props.isStandardGroup,
    hasProfessionalProject: props.hasProfessionalProject
  }

  // 应用业务策略处理
  const processedData = strategy.processData({
    ...baseProps,
    businessConfig: props.businessConfig
  })

  // 合并配置属性
  return {
    ...baseProps,
    ...processedData,
    ...config.componentProps,
    ...props.componentProps,
    // 业务配置
    tabConfig: config,
    tabStrategy: strategy,
    businessConfig: props.businessConfig
  }
})

// 处理子组件的数据变化
const handleDataChange = (data) => {
  const strategy = currentStrategy.value

  // 使用策略处理数据变化
  const processedData = strategy ? strategy.handleDataChange(data) : data

  emit('dataChange', {
    tabCode: props.currentTab?.code,
    tabName: props.currentTab?.value,
    originalData: data,
    processedData
  })
}

// 处理子组件的操作事件
const handleAction = (action, data) => {
  const strategy = currentStrategy.value

  // 使用策略处理操作事件
  const processedAction = strategy ? strategy.handleAction(action, data) : { action, data }

  emit('action', {
    tabCode: props.currentTab?.code,
    tabName: props.currentTab?.value,
    originalAction: action,
    originalData: data,
    processedAction: processedAction.action,
    processedData: processedAction.data
  })
}

// 监听Tab切换
watch(() => props.currentTab, (newTab) => {
  if (newTab?.code) {
    const strategy = currentStrategy.value
    const config = currentConfig.value

    emit('componentLoad', {
      tabCode: newTab.code,
      tabName: newTab.value,
      componentName: componentName.value,
      strategy: strategy?.name,
      config: config?.name
    })
  }
}, { immediate: true })
</script>

<style lang="scss" scoped>
.tab-content-enhanced {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;

  .no-content {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fafafa;
  }

  .component-loading {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .component-placeholder,
  .component-error {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>