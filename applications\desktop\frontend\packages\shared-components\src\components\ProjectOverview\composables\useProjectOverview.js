import { ref, computed } from 'vue'
import { getApiConfig } from '../configs/apiConfigs'

/**
 * 项目概况业务逻辑组合式API
 * @param {Object} options 配置选项
 * @param {String} options.moduleType 模块类型
 * @param {Number} options.levelType 层级类型
 * @param {Object} options.projectContext 项目上下文信息
 */
export function useProjectOverview(options = {}) {
  const { moduleType, levelType, projectContext = {} } = options

  // 状态管理
  const loading = ref(false)
  const error = ref(null)
  
  // API配置
  const apiConfig = computed(() => getApiConfig(moduleType))
  
  // 构建请求参数
  const buildRequestParams = (additionalParams = {}) => {
    const baseParams = {
      levelType,
      projectId: projectContext.projectId,
      unitId: projectContext.unitId,
      ...additionalParams
    }
    
    // 根据模块类型添加特定参数
    switch (moduleType) {
      case 'settlement':
        baseParams.settlementType = 'final'
        break
      case 'review':
        baseParams.reviewType = 'audit'
        break
      case 'material':
        baseParams.includeLabor = true
        baseParams.includeMachine = true
        break
    }
    
    return baseParams
  }
  
  // 获取基本信息
  const fetchBasicInfo = async (params = {}) => {
    try {
      loading.value = true
      error.value = null
      
      const requestParams = buildRequestParams({
        pageType: 'jbgcxx', // 基本工程信息
        ...params
      })
      
      const response = await apiConfig.value.getBasicInfo(requestParams)
      
      if (response.status === 200) {
        return processBasicInfoData(response.result || [])
      } else {
        throw new Error(response.message || '获取基本信息失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取基本信息失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 获取工程特征
  const fetchFeatureInfo = async (params = {}) => {
    try {
      loading.value = true
      error.value = null
      
      const requestParams = buildRequestParams({
        pageType: 'gctz', // 工程特征
        ...params
      })
      
      const response = await apiConfig.value.getFeatureInfo?.(requestParams) || 
                      await apiConfig.value.getBasicInfo(requestParams)
      
      if (response.status === 200) {
        return processFeatureData(response.result || [])
      } else {
        throw new Error(response.message || '获取工程特征失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('获取工程特征失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 保存基本信息
  const saveBasicInfo = async (data) => {
    try {
      loading.value = true
      error.value = null
      
      const requestParams = {
        data: Array.isArray(data) ? data : [data],
        ...buildRequestParams()
      }
      
      const response = await apiConfig.value.saveOrUpdate(requestParams)
      
      if (response.status === 200) {
        return response.result
      } else {
        throw new Error(response.message || '保存基本信息失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('保存基本信息失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 保存工程特征
  const saveFeatureInfo = async (data) => {
    try {
      loading.value = true
      error.value = null
      
      const requestParams = {
        data: Array.isArray(data) ? data : [data],
        pageType: 'gctz',
        ...buildRequestParams()
      }
      
      const response = await apiConfig.value.saveFeature?.(requestParams) || 
                      await apiConfig.value.saveOrUpdate(requestParams)
      
      if (response.status === 200) {
        return response.result
      } else {
        throw new Error(response.message || '保存工程特征失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('保存工程特征失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 删除项目
  const deleteItem = async (sequenceNbr) => {
    try {
      loading.value = true
      error.value = null
      
      const requestParams = {
        sequenceNbr,
        ...buildRequestParams()
      }
      
      const response = await apiConfig.value.delete(requestParams)
      
      if (response.status === 200) {
        return response.result
      } else {
        throw new Error(response.message || '删除失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('删除失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 锁定/解锁
  const toggleLock = async (lockFlag) => {
    try {
      loading.value = true
      error.value = null
      
      const requestParams = {
        lockFlag: lockFlag ? 1 : 0,
        ...buildRequestParams()
      }
      
      const response = await apiConfig.value.lock(requestParams)
      
      if (response.status === 200) {
        return response.result
      } else {
        throw new Error(response.message || '锁定操作失败')
      }
    } catch (err) {
      error.value = err.message
      console.error('锁定操作失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 导出数据
  const exportData = async (data) => {
    try {
      loading.value = true
      error.value = null
      
      const requestParams = {
        exportType: 'project-overview',
        data,
        ...buildRequestParams()
      }
      
      const response = await apiConfig.value.export?.(requestParams)
      
      if (response) {
        return response
      } else {
        // 如果没有专门的导出接口，生成本地导出
        return generateLocalExport(data)
      }
    } catch (err) {
      error.value = err.message
      console.error('导出失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }
  
  // 数据处理函数
  const processBasicInfoData = (data) => {
    return data.map((item, index) => ({
      ...item,
      dispNo: item.dispNo || (index + 1),
      key: item.sequenceNbr || `basic_${index}`,
      // 确保必要字段存在
      name: item.name || '',
      remark: item.remark || '',
      addFlag: item.addFlag || 0,
      lockFlag: item.lockFlag || 0,
      type: item.type || 'normal',
      groupCode: item.groupCode || 1
    }))
  }
  
  const processFeatureData = (data) => {
    return data.map((item, index) => ({
      ...item,
      dispNo: item.dispNo || (index + 1),
      key: item.sequenceNbr || `feature_${index}`,
      name: item.name || '',
      remark: item.remark || '',
      addFlag: item.addFlag || 0,
      lockFlag: item.lockFlag || 0
    }))
  }
  
  const generateLocalExport = (data) => {
    // 生成本地导出数据
    const exportData = {
      basic: data.basic || [],
      feature: data.feature || [],
      project: data.project || {},
      exportTime: new Date().toISOString(),
      moduleType,
      levelType
    }
    
    // 创建下载链接
    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json'
    })
    
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `项目概况_${moduleType}_${Date.now()}.json`
    link.click()
    
    URL.revokeObjectURL(url)
    
    return { success: true, type: 'local', data: exportData }
  }
  
  // 返回API
  return {
    // 状态
    loading,
    error,
    
    // 数据获取
    fetchBasicInfo,
    fetchFeatureInfo,
    
    // 数据保存
    saveBasicInfo,
    saveFeatureInfo,
    
    // 数据操作
    deleteItem,
    toggleLock,
    exportData,
    
    // 工具函数
    buildRequestParams,
    processBasicInfoData,
    processFeatureData
  }
}
