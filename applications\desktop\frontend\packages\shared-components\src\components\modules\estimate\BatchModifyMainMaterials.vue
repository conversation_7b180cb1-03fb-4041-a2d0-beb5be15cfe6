<!--
  概算模块 - 批量修改主材组件
  占位符组件，待后续实现
-->
<template>
  <a-modal
    v-model:open="visible"
    title="批量修改主材"
    width="800px"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="batch-modify-main-materials">
      <a-result
        status="info"
        title="功能开发中"
        sub-title="批量修改主材功能正在开发中，敬请期待"
      >
        <template #icon>
          <EditOutlined style="color: #1890ff" />
        </template>
      </a-result>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { EditOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  records: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 响应式数据
const visible = ref(props.visible)

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
const handleConfirm = () => {
  emit('confirm', { type: 'batchModifyMainMaterials', data: {} })
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
  emit('cancel')
}
</script>

<style scoped>
.batch-modify-main-materials {
  text-align: center;
  padding: 20px;
}
</style>
