<template>
  <div class="estimate-project-overview">
    <!-- 使用新的ProjectOverview共享组件 -->
    <ProjectOverview
      module-type="estimate"
      :level-type="levelType"
      :project-data="projectData"
      :tab-info="tabInfo"
      :editable="editable"
      :custom-config="customConfig"
      :permissions="permissions"
      @data-change="handleDataChange"
      @save="handleSave"
      @export="handleExport"
      @lock="handleLock"
      @action="handleAction"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ProjectOverview } from '@cost-app/shared-components'

const props = defineProps({
  levelType: {
    type: Number,
    default: 3
  },
  tabInfo: {
    type: Object,
    default: () => ({ code: 'project-overview', name: '项目概况' })
  },
  projectId: {
    type: String,
    default: ''
  },
  unitId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 响应式数据
const editable = ref(true)
const projectData = ref({
  projectName: '',
  projectCode: '',
  buildingArea: null,
  projectScale: null,
  constructionUnit: '',
  designUnit: '',
  compileDate: '',
  compiler: '',
  reviewer: '',
  reviewDate: '',
  // 概算特有字段
  estimateAccuracy: '',
  estimateStandard: ''
})

// 自定义配置
const customConfig = computed(() => ({
  // 概算模块特定配置
  hiddenFields: [], // 隐藏的字段
  validationRules: {
    projectName: [
      { required: true, message: '项目名称不能为空' }
    ],
    buildingArea: [
      { required: true, message: '建筑面积不能为空' },
      { type: 'number', min: 0, message: '建筑面积必须大于0' }
    ],
    estimateAccuracy: [
      { required: true, message: '估算精度不能为空' }
    ]
  },
  // 概算特有字段配置
  extraFields: {
    estimateAccuracy: {
      name: '估算精度',
      type: 'select',
      required: true,
      options: [
        { label: 'A级（±10%）', value: 'A' },
        { label: 'B级（±15%）', value: 'B' },
        { label: 'C级（±25%）', value: 'C' }
      ]
    },
    estimateStandard: {
      name: '概算标准',
      type: 'select',
      required: true,
      options: [
        { label: '国家标准', value: 'national' },
        { label: '行业标准', value: 'industry' },
        { label: '地方标准', value: 'local' }
      ]
    }
  }
}))

// 权限配置
const permissions = computed(() => ({
  canEdit: true,
  canDelete: true,
  canLock: true,
  canExport: true,
  canImport: false
}))

// 事件处理
const handleDataChange = (data) => {
  console.log('概算概况数据变更:', data)
  
  // 更新本地数据
  if (data.type === 'basic') {
    updateProjectData(data.data)
  } else if (data.type === 'feature') {
    updateFeatureData(data.data)
  }
  
  emit('dataChange', data)
}

const handleSave = (data) => {
  console.log('保存概算概况:', data)
  
  // 概算模块特有的保存逻辑
  const saveData = {
    ...data,
    moduleType: 'estimate',
    saveTime: new Date().toISOString()
  }
  
  // 调用概算API保存
  saveEstimateOverview(saveData)
  
  emit('action', 'save', saveData)
}

const handleExport = (data) => {
  console.log('导出概算概况:', data)
  
  // 概算模块特有的导出逻辑
  const exportData = {
    ...data,
    exportType: 'estimate-overview',
    exportTime: new Date().toISOString()
  }
  
  // 调用概算API导出
  exportEstimateOverview(exportData)
  
  emit('action', 'export', exportData)
}

const handleLock = (lockStatus) => {
  console.log('锁定状态变更:', lockStatus)
  
  // 概算模块特有的锁定逻辑
  updateLockStatus(lockStatus)
  
  emit('action', 'lock', lockStatus)
}

const handleAction = (action, data) => {
  console.log('概算概况操作:', action, data)
  emit('action', action, data)
}

// 业务逻辑函数
const updateProjectData = (data) => {
  Object.assign(projectData.value, data)
}

const updateFeatureData = (data) => {
  console.log('更新工程特征:', data)
}

const saveEstimateOverview = async (data) => {
  try {
    // 调用概算API保存概况信息
    console.log('保存概算概况成功:', data)
    message.success('概算概况保存成功')
  } catch (error) {
    console.error('保存概算概况失败:', error)
    message.error('保存失败')
  }
}

const exportEstimateOverview = async (data) => {
  try {
    // 调用概算API导出概况信息
    console.log('导出概算概况成功:', data)
    message.success('概算概况导出成功')
  } catch (error) {
    console.error('导出概算概况失败:', error)
    message.error('导出失败')
  }
}

const updateLockStatus = async (lockStatus) => {
  try {
    // 调用概算API更新锁定状态
    console.log('更新锁定状态成功:', lockStatus)
    message.success(lockStatus ? '锁定成功' : '解锁成功')
  } catch (error) {
    console.error('更新锁定状态失败:', error)
    message.error('操作失败')
  }
}

// 监听器
watch(() => props.projectId, () => {
  // 项目ID变化时重新加载数据
  loadProjectData()
}, { immediate: true })

// 生命周期
onMounted(() => {
  console.log('概算概况组件已挂载')
  loadProjectData()
})

const loadProjectData = async () => {
  if (!props.projectId) return
  
  try {
    // 加载项目基本数据
    // const data = await estimateApi.getProjectOverview(props.projectId)
    // Object.assign(projectData.value, data)
    console.log('加载项目数据:', props.projectId)
  } catch (error) {
    console.error('加载项目数据失败:', error)
    message.error('加载数据失败')
  }
}
</script>

<style lang="scss" scoped>
.estimate-project-overview {
  height: 100%;
  
  // 概算模块特有样式
  :deep(.project-overview) {
    .page-header h3 {
      color: #1890ff; // 概算模块主色调
    }
    
    .ant-tabs-tab {
      &.ant-tabs-tab-active {
        color: #1890ff;
      }
    }
    
    .field-highlight {
      color: #1890ff;
    }
  }
}
</style>
