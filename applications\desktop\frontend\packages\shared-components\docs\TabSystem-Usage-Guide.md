# Tab系统使用指南

## 概述

本系统基于**子模块独立开发 + 公共组件支撑**的架构设计，解决了概预结审不同业务模块在Tab页面上的差异化需求。

## 架构设计

### 核心理念
- 🏗️ **各子模块独立开发**：预算、概算、结算、审计各自维护自己的Tab页面
- 🔧 **公共组件提供基础设施**：TabMenu导航 + TabContainer容器 + 通用表格表单
- 💼 **业务差异在子模块内解决**：每个模块实现自己特有的业务逻辑和UI

### 目录结构

```
📁 shared-components/                 # 公共组件库
├── src/components/
│   ├── TabMenu.vue                   # 智能Tab导航组件
│   ├── TabContainer.vue              # Tab容器组件
│   ├── CostTable.vue                 # 通用表格组件
│   └── CostForm.vue                  # 通用表单组件
└── docs/
    └── TabMenu.md                    # TabMenu组件API文档

📁 budget/                            # 预算子模块
├── src/views/
│   ├── BudgetMain.vue                # 主页面（使用TabContainer）
│   └── tabs/                         # 预算专用Tab页面
│       ├── BudgetProjectOverview.vue # 预算项目概况
│       ├── BudgetItemDetail.vue      # 预算分部分项
│       ├── BudgetCostAnalysis.vue    # 预算造价分析
│       └── BudgetMaterialSummary.vue # 预算人材机汇总

📁 rough-estimate/                    # 概算子模块
├── src/views/
│   ├── EstimateMain.vue              # 主页面
│   └── tabs/                         # 概算专用Tab页面
│       ├── EstimateProjectOverview.vue
│       ├── EstimateItemDetail.vue
│       └── ...

📁 settlement/                        # 结算子模块
└── src/views/tabs/                   # 结算专用Tab页面
    ├── SettlementProjectOverview.vue
    ├── SettlementItemDetail.vue
    └── ...
```

## 快速开始

### 1. 在子模块中使用TabContainer

```vue
<!-- BudgetMain.vue -->
<template>
  <div class="budget-main">
    <!-- 其他内容 -->

    <!-- Tab容器 -->
    <TabContainer
      :tab-components="budgetTabComponents"
      :module-type="currentModule"
      :level-type="currentLevel"
      :project-data="projectData"
      :table-data="tableData"
      :is-first-level-child="isFirstLevel"
      :is-standard-group="isStandardGroup"
      :has-professional-project="hasProfessional"
      @tab-change="handleTabChange"
      @title-change="handleTitleChange"
      @data-change="handleDataChange"
      @action="handleAction"
    />
  </div>
</template>

<script setup>
import { TabContainer } from '@cost-app/shared-components'

// 导入本模块的Tab页面组件
import BudgetProjectOverview from './tabs/BudgetProjectOverview.vue'
import BudgetItemDetail from './tabs/BudgetItemDetail.vue'
import BudgetCostAnalysis from './tabs/BudgetCostAnalysis.vue'
import BudgetMaterialSummary from './tabs/BudgetMaterialSummary.vue'

// 定义Tab组件映射
const budgetTabComponents = {
  '1': BudgetProjectOverview,   // 项目概况
  '2': BudgetCostAnalysis,      // 造价分析
  '3': BudgetFeeTable,          // 取费表
  '4': BudgetItemDetail,        // 分部分项
  '6': BudgetMaterialSummary,   // 人材机汇总
}

// 控制变量
const currentModule = ref('budget')
const currentLevel = ref(3)
const isFirstLevel = ref(true)
const isStandardGroup = ref(false)
const hasProfessional = ref(true)

// 事件处理
const handleTabChange = (tabData) => {
  console.log('Tab切换:', tabData)
}

const handleDataChange = ({ tab, data }) => {
  console.log('数据变化:', tab, data)
  // 根据Tab处理数据变化
}

const handleAction = ({ tab, action, data }) => {
  console.log('操作事件:', tab, action, data)
  // 根据Tab处理操作事件
}
</script>
```

### 2. 创建业务专用的Tab页面

```vue
<!-- BudgetItemDetail.vue -->
<template>
  <div class="budget-item-detail">
    <div class="page-header">
      <h3>预算分部分项</h3>
      <div class="page-actions">
        <!-- 预算特有的操作按钮 -->
        <a-button @click="handleImportQuota">
          <template #icon><ImportOutlined /></template>
          导入定额
        </a-button>
        <a-button @click="handleQuotaQuery">
          定额查询
        </a-button>
      </div>
    </div>

    <div class="page-body">
      <CostTable
        :data="tableData"
        :columns="budgetTableColumns"
        :editable="true"
        @data-change="handleDataChange"
        @action="handleAction"
      >
        <!-- 预算特有的工具栏 -->
        <template #toolbar-left>
          <a-select v-model:value="quotaStandard" style="width: 120px">
            <a-select-option value="national">国家定额</a-select-option>
            <a-select-option value="local">地方定额</a-select-option>
          </a-select>
        </template>
      </CostTable>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { CostTable } from '@cost-app/shared-components'

// 接收来自TabContainer的属性
const props = defineProps({
  moduleType: String,
  levelType: Number,
  tabInfo: Object,
  projectData: Object,
  tableData: Array,
  // ... 其他属性
})

const emit = defineEmits(['dataChange', 'action'])

// 预算特有的状态
const quotaStandard = ref('national')

// 预算特有的表格列配置
const budgetTableColumns = computed(() => [
  { title: '项目编码', field: 'code', editable: true },
  { title: '项目名称', field: 'name', editable: true },
  { title: '定额编号', field: 'quotaCode', editable: true }, // 预算特有
  { title: '预算单价', field: 'budgetUnitPrice', editable: true }, // 预算特有
  { title: '预算合价', field: 'budgetAmount' },
  // ... 其他列
])

// 预算特有的业务逻辑
const handleImportQuota = () => {
  emit('action', 'importQuota', { quotaStandard: quotaStandard.value })
}

const handleQuotaQuery = () => {
  emit('action', 'quotaQuery', { standard: quotaStandard.value })
}

const handleDataChange = (data) => {
  // 预算特有的数据处理
  emit('dataChange', data)
}

const handleAction = (action, data) => {
  // 预算特有的操作处理
  emit('action', action, data)
}
</script>
```

## Tab代码映射

根据 `tabmenu.md` 规范，Tab代码与页面的对应关系：

| Tab代码 | 页面名称 | 说明 |
|---------|----------|------|
| '1' | ProjectOverview | 项目概况/工程概况 |
| '2' | CostAnalysis | 造价分析 |
| '3' | FeeTable | 取费表 |
| '4' | ItemDetail | 分部分项/预算书 |
| '5' | MeasureProject | 措施项目 |
| '6' | MaterialSummary | 人材机汇总/人材机调整 |
| '7' | OtherProject | 其他项目 |
| '8' | CostSummary | 费用汇总 |
| '9' | IndependentFee | 独立费 |

## 业务差异处理

### 1. 数据结构差异

不同业务模块在同一Tab下可能有不同的数据结构：

```javascript
// 预算分部分项
const budgetItemColumns = [
  { title: '定额编号', field: 'quotaCode' },
  { title: '预算单价', field: 'budgetUnitPrice' },
  { title: '预算合价', field: 'budgetAmount' },
]

// 结算分部分项
const settlementItemColumns = [
  { title: '变更单号', field: 'changeOrderNo' },
  { title: '结算单价', field: 'settlementUnitPrice' },
  { title: '结算合价', field: 'settlementAmount' },
  { title: '审查状态', field: 'auditStatus' },
]

// 概算分部分项
const estimateItemColumns = [
  { title: '概算指标', field: 'estimateIndex' },
  { title: '调整系数', field: 'adjustmentFactor' },
  { title: '概算金额', field: 'estimateAmount' },
]
```

### 2. 权限控制差异

```javascript
// 预算模块：完全可编辑
const budgetPermissions = {
  canEdit: true,
  canDelete: true,
  canImport: true,
  canExport: true
}

// 结算模块：基于审查状态
const settlementPermissions = computed(() => ({
  canEdit: currentUser.role === 'auditor' || record.auditStatus === 'draft',
  canDelete: currentUser.role === 'admin',
  canImport: false,
  canExport: true
}))

// 审计模块：主要只读
const auditPermissions = {
  canEdit: false,
  canDelete: false,
  canImport: false,
  canExport: true,
  canComment: true // 审计特有：可添加审查意见
}
```

### 3. 计算逻辑差异

```javascript
// 预算计算：基于定额
const calculateBudgetAmount = (record) => {
  return (record.quantity || 0) * (record.budgetUnitPrice || record.quotaUnitPrice || 0)
}

// 概算计算：基于指标和系数
const calculateEstimateAmount = (record) => {
  return (record.quantity || 0) * (record.estimateIndex || 0) * (record.adjustmentFactor || 1)
}

// 结算计算：考虑变更
const calculateSettlementAmount = (record) => {
  const baseAmount = (record.quantity || 0) * (record.settlementUnitPrice || 0)
  const changeAmount = record.changeOrders?.reduce((sum, change) => sum + change.amount, 0) || 0
  return baseAmount + changeAmount
}
```

## 事件通信

### TabContainer事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `tab-change` | `tabData` | Tab切换时触发 |
| `title-change` | `title` | Tab标题变化时触发 |
| `data-change` | `{ tab, data }` | Tab页面数据变化时触发 |
| `action` | `{ tab, action, data }` | Tab页面操作事件时触发 |

### Tab页面组件事件

Tab页面组件通过 `emit` 向父组件发送事件：

```javascript
// 数据变化
emit('dataChange', updatedData)

// 操作事件
emit('action', 'save', formData)
emit('action', 'export', { type: 'excel', data: tableData })
emit('action', 'importQuota', { quotaStandard: 'national' })
```

## 样式定制

### 1. 模块主题

不同模块可以有不同的主题色彩：

```vue
<TabContainer :class="`${moduleType}-theme`" />
```

```scss
// 预算模块主题
.budget-theme :deep(.ant-tabs-tab-active) {
  background-color: #f6ffed;
  border-bottom: 2px solid #52c41a;
  .ant-tabs-tab-btn { color: #52c41a; }
}

// 概算模块主题
.rough-estimate-theme :deep(.ant-tabs-tab-active) {
  background-color: #e6f7ff;
  border-bottom: 2px solid #1890ff;
  .ant-tabs-tab-btn { color: #1890ff; }
}

// 结算模块主题
.settlement-theme :deep(.ant-tabs-tab-active) {
  background-color: #f9f0ff;
  border-bottom: 2px solid #722ed1;
  .ant-tabs-tab-btn { color: #722ed1; }
}
```

### 2. Tab页面样式

每个Tab页面推荐使用统一的结构：

```scss
.tab-page {
  height: 100%;
  display: flex;
  flex-direction: column;

  .page-header {
    flex-shrink: 0;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
  }

  .page-body {
    flex: 1;
    overflow: hidden;
    padding: 16px 24px;
  }

  .page-footer {
    flex-shrink: 0;
    padding: 16px 24px;
    background: #fff;
    border-top: 1px solid #f0f0f0;
  }
}
```

## 最佳实践

### 1. 组件命名规范

```
{ModuleName}{TabName}.vue

例如：
- BudgetProjectOverview.vue    # 预算项目概况
- BudgetItemDetail.vue         # 预算分部分项
- EstimateProjectOverview.vue  # 概算项目概况
- SettlementItemDetail.vue     # 结算分部分项
```

### 2. 数据传递

- **向下传递**：通过 TabContainer 的 props 传递给 Tab页面
- **向上传递**：通过 emit 事件向父组件发送数据和操作

### 3. 状态管理

- **本地状态**：Tab页面内部状态用 `ref`/`reactive`
- **模块状态**：可使用 Pinia store 管理模块级状态
- **全局状态**：通过 shared-components 的 store 管理

### 4. 错误处理

```javascript
// Tab页面中的错误处理
const handleSave = async () => {
  try {
    await saveData()
    emit('action', 'save-success', data)
    message.success('保存成功')
  } catch (error) {
    emit('action', 'save-error', error)
    message.error('保存失败：' + error.message)
  }
}
```

### 5. 性能优化

- 使用 `v-show` 而不是 `v-if` 来切换Tab内容（如果Tab页面较重）
- 大数据量时考虑虚拟滚动
- 合理使用 `computed` 和 `watch`

## 扩展功能

### 1. 添加新的Tab页面

1. 在模块的 `tabs/` 目录下创建新组件
2. 在主页面的 `tabComponents` 中添加映射
3. 确保Tab代码符合 `tabmenu.md` 规范

### 2. 添加新的业务模块

1. 创建新的子模块目录
2. 实现该模块特有的Tab页面
3. 在 `tabmenu.md` 中定义该模块的菜单规则

### 3. 自定义Tab导航

如果默认的TabMenu不满足需求，可以自定义：

```vue
<template>
  <div class="custom-tab-container">
    <!-- 自定义Tab导航 -->
    <CustomTabMenu @tab-change="handleTabChange" />

    <!-- Tab内容 -->
    <component :is="currentComponent" />
  </div>
</template>
```

## 常见问题

### Q: 如何在不同Tab之间共享数据？

A: 通过父组件的状态管理或者使用 Pinia store：

```javascript
// 在主页面中
const sharedData = ref({})

const handleDataChange = ({ tab, data }) => {
  // 更新共享数据
  sharedData.value[tab.code] = data
}

// 传递给所有Tab页面
:shared-data="sharedData"
```

### Q: 如何处理Tab页面的权限控制？

A: 在Tab页面组件中根据用户权限控制功能：

```javascript
const permissions = computed(() => ({
  canEdit: hasPermission('budget:edit'),
  canDelete: hasPermission('budget:delete'),
}))
```

### Q: 如何实现Tab页面的懒加载？

A: 使用动态导入：

```javascript
const budgetTabComponents = {
  '1': () => import('./tabs/BudgetProjectOverview.vue'),
  '4': () => import('./tabs/BudgetItemDetail.vue'),
}
```

### Q: 如何自定义Tab的显示逻辑？

A: TabMenu组件会根据 `module-type` 和 `level-type` 自动过滤Tab，如需自定义可以：

1. 修改 `tabmenu.md` 规范
2. 或者直接在 TabContainer 中控制 `tab-components` 的映射

## 总结

这个Tab系统设计的核心优势：

✅ **高内聚低耦合**：各业务模块独立开发，互不影响
✅ **复用性强**：公共组件可在所有模块中复用
✅ **扩展性好**：新增业务模块或Tab页面都很容易
✅ **维护性强**：各模块团队可独立维护业务逻辑
✅ **灵活性高**：每个模块可根据需求定制Tab页面

通过这种架构，既保持了代码的一致性和复用性，又充分满足了不同业务模块的差异化需求。