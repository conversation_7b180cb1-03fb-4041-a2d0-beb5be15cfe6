<!--
  CostTable 右键菜单组件
  根据模块类型动态生成右键菜单
-->
<template>
  <div class="cost-table-menu">
    <a-menu @click="handleMenuClick" :selectable="false">
      <!-- 插入菜单 -->
      <a-sub-menu key="add" v-if="menuConfig.add">
        <template #title>
          <PlusOutlined />
          插入
        </template>
        <a-menu-item
          v-for="item in menuConfig.add.children"
          :key="`add-${item.code}`"
          :data-kind="item.kind"
          :disabled="!canAdd(item)"
        >
          {{ item.name }}
        </a-menu-item>
      </a-sub-menu>

      <a-menu-divider />

      <!-- 复制粘贴菜单 -->
      <a-menu-item key="copy" :disabled="!hasSelection">
        <CopyOutlined />
        复制
      </a-menu-item>
      
      <a-menu-item 
        key="copyCell" 
        v-if="showCopyCell"
        :disabled="!currentCell"
      >
        <CopyOutlined />
        复制单元格内容
      </a-menu-item>

      <a-menu-item key="paste" :disabled="!canPaste">
        <FileAddOutlined />
        粘贴
      </a-menu-item>
      
      <a-menu-item 
        key="pasteChild" 
        v-if="showPasteChild"
        :disabled="!canPasteChild"
      >
        <FileAddOutlined />
        粘贴为子项
      </a-menu-item>

      <a-menu-divider v-if="showDeleteSection" />

      <!-- 删除菜单 -->
      <a-menu-item 
        key="delete" 
        v-if="showDelete"
        :disabled="!hasSelection"
      >
        <DeleteOutlined />
        删除
      </a-menu-item>

      <a-menu-divider v-if="showLockSection" />

      <!-- 锁定菜单 -->
      <a-menu-item 
        key="lock" 
        v-if="showLock"
        :disabled="!hasSelection"
      >
        <LockOutlined />
        {{ isLocked ? '解锁' : '锁定' }}
      </a-menu-item>

      <a-menu-divider v-if="showModuleSpecific" />

      <!-- 模块特有菜单 -->
      <template v-if="moduleType === 'gaiSuan'">
        <a-menu-item key="introductionQuantity">
          <CalculatorOutlined />
          引入工程量
        </a-menu-item>
        <a-menu-item key="batchModifyMainMaterials">
          <EditOutlined />
          批量修改主材
        </a-menu-item>
        <a-menu-item key="organizeFb">
          <OrderedListOutlined />
          整理分部
        </a-menu-item>
      </template>

      <template v-if="moduleType === 'shenHe'">
        <a-menu-item key="auditComparison">
          <DiffOutlined />
          审核对比
        </a-menu-item>
        <a-menu-item key="auditOpinion">
          <CommentOutlined />
          审核意见
        </a-menu-item>
        <a-menu-item key="tempRemove" :disabled="!hasSelection">
          <EyeInvisibleOutlined />
          临时删除
        </a-menu-item>
      </template>

      <template v-if="moduleType === 'jieSuan'">
        <a-menu-item key="contractAssociation">
          <LinkOutlined />
          关联合同
        </a-menu-item>
        <a-menu-item key="progressSettlement">
          <PercentageOutlined />
          进度结算
        </a-menu-item>
      </template>

      <template v-if="moduleType === 'gongLiaoJi'">
        <a-menu-item key="standardConversion">
          <SwapOutlined />
          标准换算
        </a-menu-item>
        <a-menu-item key="setMainMaterial">
          <ToolOutlined />
          设置主材
        </a-menu-item>
        <a-menu-item key="projectAttrAssociation">
          <NodeIndexOutlined />
          项目属性关联
        </a-menu-item>
      </template>

      <a-menu-divider v-if="showSettings" />

      <!-- 设置菜单 -->
      <a-menu-item 
        key="pageColumnSetting" 
        v-if="showColumnSetting"
      >
        <SettingOutlined />
        页面显示列设置
      </a-menu-item>
    </a-menu>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import {
  PlusOutlined,
  CopyOutlined,
  FileAddOutlined,
  DeleteOutlined,
  LockOutlined,
  CalculatorOutlined,
  EditOutlined,
  OrderedListOutlined,
  DiffOutlined,
  CommentOutlined,
  EyeInvisibleOutlined,
  LinkOutlined,
  PercentageOutlined,
  SwapOutlined,
  ToolOutlined,
  NodeIndexOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import { generateMenuConfig, getModuleConfig } from './moduleConfigs.js'

// Props
const props = defineProps({
  moduleType: {
    type: String,
    required: true
  },
  currentRecord: {
    type: Object,
    default: () => ({})
  },
  currentCell: {
    type: Object,
    default: null
  },
  hasSelection: {
    type: Boolean,
    default: false
  },
  canPaste: {
    type: Boolean,
    default: false
  },
  isLocked: {
    type: Boolean,
    default: false
  },
  customMenuItems: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['menuClick'])

// 计算属性
const moduleConfig = computed(() => getModuleConfig(props.moduleType))
const menuConfig = computed(() => generateMenuConfig(props.moduleType))

// 显示条件计算
const showCopyCell = computed(() => {
  return props.moduleType === 'gaiSuan'
})

const showPasteChild = computed(() => {
  return props.moduleType === 'gaiSuan'
})

const showDeleteSection = computed(() => {
  return ['yuSuan', 'jieSuan', 'shenHe'].includes(props.moduleType)
})

const showDelete = computed(() => {
  return ['yuSuan', 'jieSuan', 'shenHe'].includes(props.moduleType)
})

const showLockSection = computed(() => {
  return moduleConfig.value.features.supportsLock
})

const showLock = computed(() => {
  return moduleConfig.value.features.supportsLock
})

const showModuleSpecific = computed(() => {
  return ['gaiSuan', 'shenHe', 'jieSuan', 'gongLiaoJi'].includes(props.moduleType)
})

const showSettings = computed(() => {
  return ['yuSuan', 'jieSuan', 'shenHe'].includes(props.moduleType)
})

const showColumnSetting = computed(() => {
  return ['yuSuan', 'jieSuan', 'shenHe'].includes(props.moduleType)
})

// 方法
const canAdd = (item) => {
  // 根据当前选中行和业务规则判断是否可以添加
  const record = props.currentRecord
  
  switch (item.kind) {
    case '01': // 分部
      return true
    case '02': // 子分部
      return record.kind === '01'
    case '03': // 清单
      return ['01', '02'].includes(record.kind)
    case '04': // 子目
      return record.kind === '03'
    case '-1': // 概算子目
      return props.moduleType === 'gaiSuan' || props.moduleType === 'gongLiaoJi'
    default:
      return true
  }
}

const canPasteChild = computed(() => {
  return props.canPaste && props.currentRecord.kind
})

// 菜单点击处理
const handleMenuClick = ({ key }) => {
  const menuData = {
    key,
    record: props.currentRecord,
    cell: props.currentCell,
    moduleType: props.moduleType
  }
  
  // 处理添加操作
  if (key.startsWith('add-')) {
    const code = key.replace('add-', '')
    const addItem = menuConfig.value.options
      .find(opt => opt.code === 'add')?.children
      .find(child => child.code == code)
    
    if (addItem) {
      menuData.action = 'add'
      menuData.addType = addItem.kind
      menuData.addName = addItem.name
    }
  }
  
  emit('menuClick', menuData)
}

// 暴露方法
defineExpose({
  canAdd,
  menuConfig
})
</script>

<style scoped>
.cost-table-menu {
  min-width: 180px;
}

.cost-table-menu :deep(.ant-menu) {
  border: none;
  box-shadow: none;
}

.cost-table-menu :deep(.ant-menu-item) {
  height: 32px;
  line-height: 32px;
  margin: 0;
}

.cost-table-menu :deep(.ant-menu-submenu-title) {
  height: 32px;
  line-height: 32px;
  margin: 0;
}

.cost-table-menu :deep(.ant-menu-item-disabled) {
  color: #00000040;
}
</style>
