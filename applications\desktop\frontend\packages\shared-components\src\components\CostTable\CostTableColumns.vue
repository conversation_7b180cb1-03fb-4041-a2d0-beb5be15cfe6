<!--
  CostTable 列配置管理组件
  负责根据模块类型动态生成和管理表格列配置
-->
<template>
  <div class="cost-table-columns">
    <!-- 列配置工具栏 -->
    <div class="columns-toolbar" v-if="showToolbar">
      <a-space>
        <a-button size="small" @click="showColumnSettings">
          <template #icon><SettingOutlined /></template>
          列设置
        </a-button>
        <a-button size="small" @click="resetColumns">
          <template #icon><ReloadOutlined /></template>
          重置
        </a-button>
      </a-space>
    </div>

    <!-- 列设置弹窗 -->
    <a-modal
      v-model:open="columnSettingsVisible"
      title="列显示设置"
      width="600px"
      @ok="applyColumnSettings"
      @cancel="cancelColumnSettings"
    >
      <div class="column-settings">
        <a-table
          :columns="settingsColumns"
          :data-source="columnSettings"
          :pagination="false"
          size="small"
          row-key="field"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'visible'">
              <a-switch
                v-model:checked="record.visible"
                size="small"
                :disabled="record.required"
              />
            </template>
            <template v-if="column.dataIndex === 'width'">
              <a-input-number
                v-model:value="record.width"
                :min="50"
                :max="500"
                size="small"
                style="width: 80px"
              />
            </template>
            <template v-if="column.dataIndex === 'order'">
              <a-space>
                <a-button
                  size="small"
                  type="text"
                  :disabled="index === 0"
                  @click="moveColumn(index, -1)"
                >
                  <template #icon><ArrowUpOutlined /></template>
                </a-button>
                <a-button
                  size="small"
                  type="text"
                  :disabled="index === columnSettings.length - 1"
                  @click="moveColumn(index, 1)"
                >
                  <template #icon><ArrowDownOutlined /></template>
                </a-button>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  SettingOutlined,
  ReloadOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined
} from '@ant-design/icons-vue'
import { generateColumnConfig, getModuleConfig, createEditableChecker } from './moduleConfigs.js'

// Props
const props = defineProps({
  moduleType: {
    type: String,
    required: true
  },
  baseColumns: {
    type: Array,
    default: () => []
  },
  showToolbar: {
    type: Boolean,
    default: false
  },
  customColumns: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['columnsChange', 'columnUpdate'])

// 响应式数据
const columnSettingsVisible = ref(false)
const columnSettings = ref([])
const originalSettings = ref([])

// 列设置表格配置
const settingsColumns = [
  {
    title: '列名',
    dataIndex: 'title',
    width: 150
  },
  {
    title: '显示',
    dataIndex: 'visible',
    width: 80,
    align: 'center'
  },
  {
    title: '宽度',
    dataIndex: 'width',
    width: 100,
    align: 'center'
  },
  {
    title: '排序',
    dataIndex: 'order',
    width: 100,
    align: 'center'
  }
]

// 计算属性：生成最终的列配置
const computedColumns = computed(() => {
  const moduleConfig = getModuleConfig(props.moduleType)
  const editableChecker = createEditableChecker(props.moduleType)
  
  // 获取基础列配置
  let baseColumns = props.baseColumns.length > 0 
    ? props.baseColumns 
    : generateColumnConfig(props.moduleType)
  
  // 合并自定义列
  if (props.customColumns.length > 0) {
    baseColumns = [...baseColumns, ...props.customColumns]
  }
  
  // 应用编辑条件和模块特定配置
  const processedColumns = baseColumns.map(column => {
    const processedColumn = { ...column }
    
    // 设置编辑条件
    if (processedColumn.editable) {
      switch (processedColumn.field) {
        case moduleConfig.codeField:
          processedColumn.editable = ({ record }) => {
            return editableChecker.codeEditable(record) ? 'cellEditorSlot' : false
          }
          break
        case 'type':
          processedColumn.editable = ({ record }) => {
            return editableChecker.typeEditable(record) ? 'cellEditorSlot' : false
          }
          break
        case 'projectAttr':
          processedColumn.editable = ({ record }) => {
            return editableChecker.projectAttrEditable(record) ? 'cellEditorSlot' : false
          }
          break
        default:
          // 其他字段的编辑条件
          processedColumn.editable = ({ record }) => {
            return !record.isLocked && record.isTempRemove !== 1 ? 'cellEditorSlot' : false
          }
      }
    }
    
    // 设置列的其他属性
    if (processedColumn.treeNode && processedColumn.field === moduleConfig.codeField) {
      processedColumn.treeNode = true
    }
    
    return processedColumn
  })
  
  // 过滤隐藏的列
  return processedColumns.filter(column => !column.hidden)
})

// 监听模块类型变化
watch(() => props.moduleType, (newType) => {
  initializeColumnSettings()
  emit('columnsChange', computedColumns.value)
}, { immediate: true })

// 监听列配置变化
watch(computedColumns, (newColumns) => {
  emit('columnsChange', newColumns)
})

// 初始化列设置
const initializeColumnSettings = () => {
  const columns = generateColumnConfig(props.moduleType)
  columnSettings.value = columns.map((column, index) => ({
    field: column.field,
    title: column.title,
    visible: !column.hidden,
    width: column.width || 100,
    required: column.required || false,
    order: index
  }))
  originalSettings.value = JSON.parse(JSON.stringify(columnSettings.value))
}

// 显示列设置弹窗
const showColumnSettings = () => {
  initializeColumnSettings()
  columnSettingsVisible.value = true
}

// 应用列设置
const applyColumnSettings = () => {
  // 更新列配置
  const updatedColumns = computedColumns.value.map(column => {
    const setting = columnSettings.value.find(s => s.field === column.field)
    if (setting) {
      return {
        ...column,
        hidden: !setting.visible,
        width: setting.width
      }
    }
    return column
  })
  
  emit('columnUpdate', updatedColumns)
  columnSettingsVisible.value = false
}

// 取消列设置
const cancelColumnSettings = () => {
  columnSettings.value = JSON.parse(JSON.stringify(originalSettings.value))
  columnSettingsVisible.value = false
}

// 重置列配置
const resetColumns = () => {
  initializeColumnSettings()
  emit('columnsChange', computedColumns.value)
}

// 移动列位置
const moveColumn = (index, direction) => {
  const newIndex = index + direction
  if (newIndex >= 0 && newIndex < columnSettings.value.length) {
    const temp = columnSettings.value[index]
    columnSettings.value[index] = columnSettings.value[newIndex]
    columnSettings.value[newIndex] = temp
    
    // 更新排序
    columnSettings.value.forEach((item, idx) => {
      item.order = idx
    })
  }
}

// 暴露方法
defineExpose({
  getColumns: () => computedColumns.value,
  resetColumns,
  showColumnSettings
})
</script>

<style scoped>
.cost-table-columns {
  width: 100%;
}

.columns-toolbar {
  margin-bottom: 8px;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.column-settings {
  max-height: 400px;
  overflow-y: auto;
}

.column-settings :deep(.ant-table-tbody > tr > td) {
  padding: 8px;
}
</style>
