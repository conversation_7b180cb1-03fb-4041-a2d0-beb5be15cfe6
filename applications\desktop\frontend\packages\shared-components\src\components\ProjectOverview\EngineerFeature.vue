<template>
  <div class="engineer-feature">
    <div class="toolbar" v-if="showToolbar">
      <a-space>
        <a-button @click="handleAdd" :disabled="!editable" size="small">
          <template #icon><PlusOutlined /></template>
          新增特征
        </a-button>
        <a-button @click="handleRefresh" size="small">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </a-space>
    </div>

    <div class="table-container">
      <s-table
        ref="tableRef"
        :columns="tableColumns"
        :data-source="tableData"
        :loading="loading"
        :tree-config="treeConfig"
        :edit-config="editConfig"
        :row-config="rowConfig"
        :scroll-y="{ enabled: true }"
        @cell-click="handleCellClick"
        @cell-dblclick="handleCellDblClick"
        @edit-closed="handleEditClosed"
        @context-menu="handleContextMenu"
      >
        <!-- 序号列 -->
        <template #bodyCell_dispNo="{ record, rowIndex }">
          <span>{{ getDisplayIndex(record, rowIndex) }}</span>
        </template>

        <!-- 特征名称列 -->
        <template #bodyCell_name="{ record }">
          <span :class="getNameClass(record)">{{ record.name }}</span>
        </template>

        <!-- 特征描述列（可编辑） -->
        <template #bodyCell_remark="{ record, column }">
          <template v-if="isCellEditable(record, column)">
            <a-textarea
              v-model:value="record.remark"
              :placeholder="getPlaceholder(record)"
              :rows="2"
              :maxlength="500"
              show-count
              @change="handleFieldChange(record, column)"
              @blur="handleFieldBlur(record, column)"
            />
          </template>
          <template v-else>
            <div class="feature-content" :class="getRemarkClass(record)">
              {{ record.remark || '暂无描述' }}
            </div>
          </template>
        </template>
      </s-table>
    </div>

    <!-- 右键菜单 -->
    <a-dropdown
      v-model:open="contextMenuVisible"
      :trigger="['contextmenu']"
      placement="bottomLeft"
    >
      <div></div>
      <template #overlay>
        <a-menu @click="handleMenuClick">
          <a-menu-item key="add" :disabled="!editable">
            <PlusOutlined />
            新增特征
          </a-menu-item>
          <a-menu-item key="delete" :disabled="!canDelete">
            <DeleteOutlined />
            删除
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="copy">
            <CopyOutlined />
            复制
          </a-menu-item>
          <a-menu-item key="paste" :disabled="!canPaste">
            <FileAddOutlined />
            粘贴
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup>
import { ref, computed, inject, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  DeleteOutlined,
  CopyOutlined,
  FileAddOutlined
} from '@ant-design/icons-vue'
import { useEngineerFeature } from './composables/useEngineerFeature'
import { mockFeatureData, getFeatureDataByModule } from './mockData/featureData'

const props = defineProps({
  moduleType: String,
  levelType: Number,
  projectData: Object,
  tableData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: true
  },
  showToolbar: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['dataChange', 'save', 'delete', 'refresh'])

// 注入上下文
const context = inject('projectOverviewContext', {})

// 组合式API
const {
  selectedRecord,
  clipboard,
  addNewFeature,
  deleteFeature,
  copyFeature,
  pasteFeature,
  canPaste
} = useEngineerFeature()

// 响应式数据
const tableRef = ref()
const contextMenuVisible = ref(false)
const currentRecord = ref(null)

// 表格配置
const tableColumns = computed(() => [
  { 
    field: 'dispNo', 
    title: '序号', 
    width: 60, 
    align: 'center',
    slots: { bodyCell: 'bodyCell_dispNo' }
  },
  { 
    field: 'name', 
    title: '特征名称', 
    width: 200, 
    align: 'left',
    slots: { bodyCell: 'bodyCell_name' }
  },
  { 
    field: 'remark', 
    title: '特征描述', 
    minWidth: 400, 
    align: 'left',
    slots: { bodyCell: 'bodyCell_remark' }
  }
])

const treeConfig = computed(() => ({
  children: 'childrenList',
  indent: 20,
  showIcon: false,
  accordion: false
}))

const editConfig = computed(() => ({
  trigger: 'dblclick',
  mode: 'cell',
  showStatus: true
}))

const rowConfig = computed(() => ({
  keyField: 'sequenceNbr',
  isHover: true
}))

// 计算属性
const canDelete = computed(() => {
  return props.editable && currentRecord.value && currentRecord.value.addFlag
})

// 工程特征预定义选项
const featureOptions = computed(() => {
  const baseFeatures = [
    '结构类型',
    '基础类型',
    '装修标准',
    '层数',
    '层高',
    '抗震设防烈度',
    '建筑等级',
    '耐火等级',
    '屋面类型',
    '外墙类型',
    '门窗类型',
    '电梯配置',
    '空调系统',
    '消防系统',
    '智能化系统'
  ]
  
  // 根据模块类型添加特定特征
  switch (props.moduleType) {
    case 'material':
      return [...baseFeatures, '主要材料', '施工工艺', '质量标准']
    case 'settlement':
      return [...baseFeatures, '变更情况', '签证情况', '索赔情况']
    default:
      return baseFeatures
  }
})

// 样式类名
const getNameClass = (record) => {
  return {
    'feature-title': record.type === 'title',
    'feature-category': record.type === 'category',
    'feature-item': record.type === 'item' || !record.type
  }
}

const getRemarkClass = (record) => {
  return {
    'feature-locked': record.lockFlag,
    'feature-empty': !record.remark
  }
}

const getDisplayIndex = (record, rowIndex) => {
  if (record.type === 'title' || record.type === 'category') return ''
  return record.dispNo || (rowIndex + 1)
}

const getPlaceholder = (record) => {
  return `请描述${record.name}的具体情况`
}

// 编辑权限检查
const isCellEditable = (record, column) => {
  if (!props.editable || record.lockFlag) return false
  if (column.field !== 'remark') return false
  
  // 标题和分类不可编辑
  if (record.type === 'title' || record.type === 'category') return false
  
  return true
}

// 事件处理
const handleCellClick = (params) => {
  const { record } = params
  currentRecord.value = record
  selectedRecord.value = record
}

const handleCellDblClick = (params) => {
  const { record, column } = params
  if (isCellEditable(record, column)) {
    nextTick(() => {
      tableRef.value?.setActiveCell(record, column.field)
    })
  }
}

const handleEditClosed = (params) => {
  const { record, column } = params
  handleFieldChange(record, column)
}

const handleFieldChange = (record, column) => {
  // 触发数据变更事件
  emit('dataChange', props.tableData)
  
  // 自动保存
  handleAutoSave(record)
}

const handleFieldBlur = (record, column) => {
  // 失焦时可以进行一些格式化处理
  if (record.remark) {
    record.remark = record.remark.trim()
  }
}

const handleAutoSave = async (record) => {
  try {
    emit('save', [record])
  } catch (error) {
    console.error('自动保存失败:', error)
  }
}

const handleContextMenu = (params) => {
  const { record } = params
  currentRecord.value = record
  contextMenuVisible.value = true
}

const handleMenuClick = ({ key }) => {
  contextMenuVisible.value = false
  
  switch (key) {
    case 'add':
      handleAdd()
      break
    case 'delete':
      handleDelete()
      break
    case 'copy':
      handleCopy()
      break
    case 'paste':
      handlePaste()
      break
  }
}

const handleAdd = () => {
  // 显示特征选择对话框
  showFeatureSelector()
}

const handleDelete = () => {
  if (!currentRecord.value || !currentRecord.value.addFlag) {
    message.warning('只能删除新增的特征')
    return
  }
  
  emit('delete', currentRecord.value.sequenceNbr)
}

const handleCopy = () => {
  copyFeature(currentRecord.value)
  message.success('已复制到剪贴板')
}

const handlePaste = () => {
  const newRecord = pasteFeature(currentRecord.value)
  if (newRecord) {
    emit('dataChange', [...props.tableData, newRecord])
    message.success('粘贴成功')
  }
}

const handleRefresh = () => {
  emit('refresh')
}

// 显示特征选择器
const showFeatureSelector = () => {
  // 这里可以实现一个特征选择对话框
  // 暂时使用简单的输入框
  const featureName = prompt('请输入特征名称:', '')
  if (featureName && featureName.trim()) {
    const newRecord = addNewFeature(currentRecord.value, featureName.trim())
    emit('dataChange', [...props.tableData, newRecord])
  }
}

// 初始化数据
const initializeData = () => {
  if (!props.tableData || props.tableData.length === 0) {
    // 如果没有传入数据，使用模拟数据
    const mockData = getFeatureDataByModule(props.moduleType)
    emit('dataChange', mockData)
  }
}

// 监听器
watch(() => props.tableData, (newData) => {
  nextTick(() => {
    tableRef.value?.loadData(newData)
  })
}, { deep: true, immediate: true })

watch(() => props.moduleType, () => {
  // 模块类型变化时重新初始化数据
  initializeData()
}, { immediate: true })
</script>

<style lang="scss" scoped>
.engineer-feature {
  height: 100%;
  display: flex;
  flex-direction: column;

  .toolbar {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
  }

  .table-container {
    flex: 1;
    overflow: hidden;
  }

  // 特征样式
  :deep(.feature-title) {
    font-weight: bold;
    font-size: 14px;
    color: #1890ff;
  }

  :deep(.feature-category) {
    font-weight: 600;
    color: #333;
    background-color: #f8f9fa;
  }

  :deep(.feature-item) {
    color: #666;
  }

  .feature-content {
    min-height: 40px;
    padding: 4px 0;
    line-height: 1.5;
    word-break: break-word;
  }

  :deep(.feature-locked) {
    background-color: #f0f0f0;
    color: #999;
  }

  :deep(.feature-empty) {
    color: #bfbfbf;
    font-style: italic;
  }

  // 表格样式
  :deep(.s-table) {
    .s-table-header {
      background-color: #fafafa;
    }

    .s-table-body {
      .s-table-row:hover {
        background-color: #f5f5f5;
      }

      .s-table-row.row-selected {
        background-color: #e6f7ff;
      }
    }
  }
}
</style>
