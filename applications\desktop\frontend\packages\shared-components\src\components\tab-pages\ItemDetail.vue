<template>
  <div class="tab-page-content item-detail">
    <div class="page-header">
      <h3>{{ pageTitle }}</h3>
      <div class="page-actions">
        <a-button @click="handleAddItem">
          <template #icon><PlusOutlined /></template>
          新增项目
        </a-button>
        <a-button @click="handleCalculate">
          <template #icon><CalculatorOutlined /></template>
          重新计算
        </a-button>
        <a-button @click="handleExport">
          <template #icon><ExportOutlined /></template>
          导出
        </a-button>
      </div>
    </div>

    <div class="page-body">
      <CostTable
        :data="processedTableData"
        :columns="tableColumns"
        :editable="true"
        :show-toolbar="true"
        :show-summary="true"
        :range-selection="true"
        :tree-props="treeProps"
        :scroll-config="{ x: 1400, y: 'calc(100vh - 400px)' }"
        :table-type="moduleType"
        row-key="id"
        :default-expand-all="false"
        @data-change="handleDataChange"
        @cell-edited="handleCellEdited"
        @row-select="handleRowSelect"
        @add-row="handleAddRow"
        @delete-row="handleDeleteRow"
        @copy-rows="handleCopyRows"
        @paste-rows="handlePasteRows"
      >
        <!-- 自定义右键菜单 -->
        <template #context-menu="{ record, column }">
          <a-menu @click="(e) => handleContextMenuClick(e, record)">
            <a-sub-menu key="add" title="插入">
              <a-menu-item key="add-parent">添加父级项目</a-menu-item>
              <a-menu-item key="add-child">添加子项目</a-menu-item>
              <a-menu-item key="add-sibling">添加同级项目</a-menu-item>
            </a-sub-menu>
            <a-menu-divider />
            <a-menu-item key="copy">复制</a-menu-item>
            <a-menu-item key="paste">粘贴</a-menu-item>
            <a-menu-divider />
            <a-menu-item key="edit">编辑</a-menu-item>
            <a-menu-item key="delete" danger>删除</a-menu-item>
            <a-menu-divider />
            <a-menu-item key="calculate">重新计算</a-menu-item>
          </a-menu>
        </template>

        <!-- 自定义工具栏 -->
        <template #toolbar-left>
          <a-space>
            <a-button size="small" @click="handleExpandAll">
              <template #icon><NodeExpandOutlined /></template>
              展开全部
            </a-button>
            <a-button size="small" @click="handleCollapseAll">
              <template #icon><NodeCollapseOutlined /></template>
              收起全部
            </a-button>
          </a-space>
        </template>

        <template #toolbar-right>
          <a-space>
            <a-tooltip title="刷新数据">
              <a-button size="small" @click="handleRefresh">
                <template #icon><ReloadOutlined /></template>
              </a-button>
            </a-tooltip>
            <a-tooltip title="表格设置">
              <a-button size="small">
                <template #icon><SettingOutlined /></template>
              </a-button>
            </a-tooltip>
          </a-space>
        </template>
      </CostTable>
    </div>

    <!-- 汇总信息 -->
    <div class="page-footer">
      <a-card size="small">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic
              title="项目数量"
              :value="summaryData.itemCount"
              suffix="项"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              :title="`合计${amountLabel}`"
              :value="summaryData.totalAmount"
              :precision="2"
              suffix="元"
              :value-style="{ color: '#cf1322' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="人工费"
              :value="summaryData.laborCost"
              :precision="2"
              suffix="元"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="材料费"
              :value="summaryData.materialCost"
              :precision="2"
              suffix="元"
            />
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  CalculatorOutlined,
  ExportOutlined,
  NodeExpandOutlined,
  NodeCollapseOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'
import CostTable from '../CostTable.vue'

const props = defineProps({
  moduleType: {
    type: String,
    required: true
  },
  levelType: {
    type: Number,
    required: true
  },
  tabInfo: {
    type: Object,
    default: () => ({})
  },
  projectData: {
    type: Object,
    default: () => ({})
  },
  tableData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 表格引用
const costTableRef = ref()

// 处理后的表格数据
const processedTableData = ref([])

// 汇总数据
const summaryData = ref({
  itemCount: 0,
  totalAmount: 0,
  laborCost: 0,
  materialCost: 0,
  mechanicalCost: 0
})

// 计算属性
const pageTitle = computed(() => {
  if (props.moduleType === 'rough-estimate' && props.levelType === 3) {
    return '预算书'
  } else if (props.moduleType === 'material' && props.levelType === 3) {
    return '预算书'
  }
  return '分部分项'
})

const amountLabel = computed(() => {
  const labels = {
    'budget': '预算金额',
    'rough-estimate': '概算金额',
    'settlement': '结算金额',
    'material': '工料机金额'
  }
  return labels[props.moduleType] || '金额'
})

// 表格列配置
const tableColumns = computed(() => {
  const baseColumns = [
    {
      title: '项目编码',
      field: 'code',
      dataIndex: 'code',
      width: 200,
      align: 'left',
      fixed: 'left',
      editable: true,
      required: true
    },
    {
      title: '项目名称',
      field: 'name',
      dataIndex: 'name',
      width: 300,
      align: 'left',
      editable: true,
      required: true,
      sorter: true
    },
    {
      title: '规格型号',
      field: 'specification',
      dataIndex: 'specification',
      width: 150,
      align: 'left',
      editable: true
    },
    {
      title: '单位',
      field: 'unit',
      dataIndex: 'unit',
      width: 80,
      align: 'center',
      editable: true
    },
    {
      title: '数量',
      field: 'quantity',
      dataIndex: 'quantity',
      width: 100,
      align: 'right',
      editable: true,
      dataType: 'number',
      formatter: (value) => Number(value || 0).toFixed(3)
    },
    {
      title: '单价',
      field: 'unitPrice',
      dataIndex: 'unitPrice',
      width: 120,
      align: 'right',
      editable: true,
      dataType: 'number',
      formatter: (value) => Number(value || 0).toFixed(2)
    },
    {
      title: '合价',
      field: 'amount',
      dataIndex: 'amount',
      width: 150,
      align: 'right',
      sorter: true,
      formatter: (value) => Number(value || 0).toFixed(2)
    }
  ]

  // 根据模块类型添加特定列
  if (props.moduleType === 'material') {
    baseColumns.splice(-1, 0, {
      title: '人工费',
      field: 'laborCost',
      dataIndex: 'laborCost',
      width: 120,
      align: 'right',
      editable: true,
      dataType: 'number',
      formatter: (value) => Number(value || 0).toFixed(2)
    }, {
      title: '材料费',
      field: 'materialCost',
      dataIndex: 'materialCost',
      width: 120,
      align: 'right',
      editable: true,
      dataType: 'number',
      formatter: (value) => Number(value || 0).toFixed(2)
    }, {
      title: '机械费',
      field: 'mechanicalCost',
      dataIndex: 'mechanicalCost',
      width: 120,
      align: 'right',
      editable: true,
      dataType: 'number',
      formatter: (value) => Number(value || 0).toFixed(2)
    })
  }

  baseColumns.push({
    title: '备注',
    field: 'remark',
    dataIndex: 'remark',
    width: 200,
    align: 'left',
    editable: true
  })

  return baseColumns
})

// 树形表格配置
const treeProps = {
  children: 'children',
  hasChildren: 'hasChildren'
}

// 事件处理
const handleDataChange = (newData) => {
  processedTableData.value = newData
  calculateSummary()
  emit('dataChange', newData)
}

const handleCellEdited = ({ record, column, newValue, oldValue }) => {
  console.log('单元格编辑:', { record, column, newValue, oldValue })

  // 自动计算合价
  if (column.field === 'quantity' || column.field === 'unitPrice') {
    record.amount = (record.quantity || 0) * (record.unitPrice || 0)
  }

  // 工料机模块特殊计算
  if (props.moduleType === 'material') {
    if (['laborCost', 'materialCost', 'mechanicalCost'].includes(column.field)) {
      record.amount = (record.laborCost || 0) + (record.materialCost || 0) + (record.mechanicalCost || 0)
    }
  }

  calculateSummary()
  message.success('编辑成功')
}

const handleRowSelect = (selectedKeys, selectedRows) => {
  emit('action', 'rowSelect', { selectedKeys, selectedRows })
}

const handleAddRow = (newRow) => {
  emit('action', 'addRow', newRow)
  message.success('新增成功')
}

const handleDeleteRow = (deletedRows) => {
  emit('action', 'deleteRow', deletedRows)
  const count = Array.isArray(deletedRows) ? deletedRows.length : 1
  message.success(`删除了 ${count} 项`)
}

const handleCopyRows = (copiedRows) => {
  message.info(`已复制 ${copiedRows.length} 项`)
}

const handlePasteRows = (pastedRows) => {
  message.success(`已粘贴 ${pastedRows.length} 项`)
}

const handleContextMenuClick = ({ key }, record) => {
  emit('action', 'contextMenu', { key, record })

  switch (key) {
    case 'add-parent':
    case 'add-child':
    case 'add-sibling':
    case 'copy':
    case 'paste':
    case 'edit':
    case 'delete':
      message.info(`执行操作: ${key}`)
      break
    case 'calculate':
      handleCalculate()
      break
  }
}

const handleAddItem = () => {
  emit('action', 'addItem')
}

const handleCalculate = () => {
  // 递归计算所有项目的金额
  const calculateNode = (node) => {
    if (node.children && node.children.length > 0) {
      node.amount = node.children.reduce((sum, child) => {
        return sum + calculateNode(child)
      }, 0)
    } else {
      node.amount = (node.quantity || 0) * (node.unitPrice || 0)

      // 工料机模块特殊处理
      if (props.moduleType === 'material') {
        node.amount = (node.laborCost || 0) + (node.materialCost || 0) + (node.mechanicalCost || 0)
      }
    }
    return node.amount
  }

  processedTableData.value.forEach(calculateNode)
  calculateSummary()
  message.success('重新计算完成')
}

const handleExport = () => {
  emit('action', 'export', {
    type: 'item-detail',
    data: processedTableData.value
  })
}

const handleExpandAll = () => {
  // 这里需要调用CostTable的展开方法
  message.info('展开全部')
}

const handleCollapseAll = () => {
  // 这里需要调用CostTable的收起方法
  message.info('收起全部')
}

const handleRefresh = () => {
  emit('action', 'refresh')
  message.success('数据已刷新')
}

// 计算汇总信息
const calculateSummary = () => {
  const flattenData = (items) => {
    let result = []
    items.forEach(item => {
      result.push(item)
      if (item.children && item.children.length > 0) {
        result = result.concat(flattenData(item.children))
      }
    })
    return result
  }

  const allItems = flattenData(processedTableData.value)

  summaryData.value = {
    itemCount: allItems.length,
    totalAmount: allItems.reduce((sum, item) => sum + (item.amount || 0), 0),
    laborCost: allItems.reduce((sum, item) => sum + (item.laborCost || 0), 0),
    materialCost: allItems.reduce((sum, item) => sum + (item.materialCost || 0), 0),
    mechanicalCost: allItems.reduce((sum, item) => sum + (item.mechanicalCost || 0), 0)
  }
}

// 初始化数据
const initializeData = () => {
  if (props.tableData && props.tableData.length > 0) {
    processedTableData.value = [...props.tableData]
    calculateSummary()
  }
}

// 监听数据变化
watch(() => props.tableData, () => {
  initializeData()
}, { deep: true })

onMounted(() => {
  initializeData()
})
</script>

<style lang="scss" scoped>
.item-detail {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      color: #333;
      font-weight: 600;
    }

    .page-actions {
      display: flex;
      gap: 8px;
    }
  }

  .page-body {
    flex: 1;
    overflow: hidden;
    margin-bottom: 16px;

    :deep(.ant-table-wrapper) {
      height: 100%;
    }
  }

  .page-footer {
    flex-shrink: 0;

    .ant-card {
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
    }

    :deep(.ant-statistic) {
      .ant-statistic-title {
        font-size: 12px;
        margin-bottom: 4px;
      }

      .ant-statistic-content {
        font-size: 16px;
      }
    }
  }
}
</style>