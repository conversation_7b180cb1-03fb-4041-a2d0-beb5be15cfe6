[workspace]
resolver = "2"
members = [
    "applications/api",
    "applications/desktop/tauri",
    "applications/desktop/tauri-plugin-api",
    "applications/desktop/frontend/src-tauri",
    "applications/nodejs",
    "crates/web",
    "crates/budget",
    "crates/budget-core",
    "crates/common",
    "crates/extensions/*",
    "crates/infrastructure/*",
    "tools/*",
    "tests",
]
[workspace.package]
version = "0.1.0"
edition = "2024"
authors = ["string<<EMAIL>>"]
description = "计价软件核心，包含计价引擎、计价规则、计价存储"
license = "MIT"

[workspace.dependencies]
serde = { version = "1.0", features = ["derive"] } # 序列化/反序列化
serde_json = "1.0"   # 序列化/反序列化
tokio = { version = "1.0", features = ["full"] }     # 异步运行时
anyhow = "1.0"   # 错误处理
thiserror = "2.0.12"  # 错误处理
imbl = { version = "6.0.0", features = ["serde"] }
async-channel="2.3.1"    # 异步通道
async-trait="0.1.88"     # 异步trait
chrono="0.4"      # 时间处理
lazy_static="1.5.0"      # 静态变量
rbs = "=4.5.25"
rbatis = "=4.5.51"
rbdc-sqlite = "=4.5.9"

rayon = "1.8"    # 并行计算
tempfile = "3.8"     # 临时文件
bincode = "2.0.1"   # 高性能序列化/反序列化
dashmap = "6.1.0"   # 高性能并发哈希表
crossbeam = "0.8.4" # 高级并发原语
memmap2 = "0.9.0"   # 内存映射文件
crossbeam-deque = "0.8.3" # 高性能工作窃取队列
crossbeam-channel = "0.5.10" # 高性能并发通道
num_cpus = "1.16.0" # CPU核心数检测
bytes = "1.9.0" # 高性能字节处理
#axum
axum="0.8.3"     # 构建web应用
mime = "0.3.16"
tower = "0.5.2"
tower-service = "0.3.3" # 构建web应用
tower-http = "0.6.3" # HTTP 中间件
futures="0.3"

# 工具模块
uuid="1"     # 生成唯一标识
zstd="0.13.3" # 高性能压缩算法
ahash="0.8.11" # 高性能哈希算法
getset="0.1.5" # 获取和设置
state={version = "0.6.0", features = ["tls"]} # 状态管理
tokio-cron-scheduler="0.14.0" # 定时任务
validator = { version = "0.20.0", features = ["derive"] } # 参数校验

# 核心模块
moduforge-model = { path = "../moduforge-rs/crates/model" }
moduforge-state = { path = "../moduforge-rs/crates/state" }
moduforge-transform = { path = "../moduforge-rs/crates/transform" }
moduforge-core = { path = "../moduforge-rs/crates/core" }
moduforge-macros = { path = "../moduforge-rs/crates/macro" }
moduforge-macros-derive = { path = "../moduforge-rs/crates/derive" }
moduforge-collaboration-client = { path = "../moduforge-rs/crates/collaboration_client" }


# 规则引擎
zen-engine = {git="https://github.com/Cassielxd/zen.git",branch="50"}
zen-expression = {git="https://github.com/Cassielxd/zen.git",branch="50"}
zen-tmpl = {git="https://github.com/Cassielxd/zen.git",branch="50"}
#zen-engine = {path="../zen/core/engine"}
#zen-expression = {path="../zen/core/expression"}
#zen-tmpl = {path="../zen/core/template"}

# 引入crates 下面的所有库
#预算
price-budget= { path = "crates/budget" }
#预算核心库
price-budget-core = { path = "crates/budget-core" }
#公共类型库
price-common = { path = "crates/common" }
#规则引擎
price-rules = { path = "crates/infrastructure/rules" }
#存储
price-storage = { path = "crates/infrastructure/storage" }
#基础数据搜索服务
price-search = { path = "crates/infrastructure/search" }
shared = { path = "crates/infrastructure/shared" }


# 引入packages 下面的所有库
price-web = { path = "crates/web" }
#工程项目 基础节点定义
extension-base-schema = { path = "crates/extensions/extension-base-schema" }
#单价构成
extension-djgc = { path = "crates/extensions/extension-djgc" }
#标准换算
extension-bzhs = { path = "crates/extensions/extension-bzhs" }
extension-cost-collect = { path = "crates/extensions/extension-cost-collect" }
#分部分项措施项目
extension-fbfx-csxm = { path = "crates/extensions/extension-fbfx-csxm" }
#人材机
extension-rcj = { path = "crates/extensions/extension-rcj" }
#人材机汇总
extension-rcj-collect = { path = "crates/extensions/extension-rcj-collect" }

# 所有扩展的接口
extension-djgc-interface = { path = "crates/extensions/extension-djgc-interface" }
extension-bzhs-interface = { path = "crates/extensions/extension-bzhs-interface" }
extension-fbfx-csxm-interface = { path = "crates/extensions/extension-fbfx-csxm-interface" }
extension-rcj-collect-interface = { path = "crates/extensions/extension-rcj-collect-interface" }
extension-rcj-interface = { path = "crates/extensions/extension-rcj-interface" }
extension-cost-collect-interface = { path = "crates/extensions/extension-cost-collect-interface" }


