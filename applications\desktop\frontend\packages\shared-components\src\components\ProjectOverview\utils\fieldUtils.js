/**
 * 字段工具函数
 * 提供字段处理相关的工具方法
 */

import { validateField as validateFieldConfig, formatFieldValue as formatFieldConfig } from '../configs/fieldConfigs'

/**
 * 格式化数值
 * @param {Number|String} value 数值
 * @param {Number} precision 精度
 * @param {String} suffix 后缀
 * @returns {String} 格式化后的数值
 */
export function formatNumber(value, precision = 2, suffix = '') {
  if (value === null || value === undefined || value === '') {
    return ''
  }
  
  const num = Number(value)
  if (isNaN(num)) {
    return value
  }
  
  const formatted = num.toFixed(precision)
  return suffix ? `${formatted} ${suffix}` : formatted
}

/**
 * 格式化日期
 * @param {String|Date} value 日期值
 * @param {String} format 格式
 * @returns {String} 格式化后的日期
 */
export function formatDate(value, format = 'YYYY-MM-DD') {
  if (!value) return ''
  
  try {
    const date = new Date(value)
    if (isNaN(date.getTime())) return value
    
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hours = String(date.getHours()).padStart(2, '0')
    const minutes = String(date.getMinutes()).padStart(2, '0')
    const seconds = String(date.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds)
  } catch (error) {
    console.error('日期格式化错误:', error)
    return value
  }
}

/**
 * 解析数值字符串
 * @param {String} value 数值字符串
 * @returns {Number|null} 解析后的数值
 */
export function parseNumber(value) {
  if (value === null || value === undefined || value === '') {
    return null
  }
  
  // 移除非数字字符（保留小数点和负号）
  const cleanValue = String(value).replace(/[^-\d.]/g, '')
  const num = Number(cleanValue)
  
  return isNaN(num) ? null : num
}

/**
 * 验证字段值
 * @param {Any} value 字段值
 * @param {Object} config 字段配置
 * @returns {Boolean} 是否有效
 */
export function validateField(value, config) {
  if (!config) return true
  
  const result = validateFieldConfig(value, config)
  return result.valid
}

/**
 * 格式化字段值用于显示
 * @param {Any} value 字段值
 * @param {Object} config 字段配置
 * @returns {String} 格式化后的值
 */
export function formatValue(value, config) {
  if (!config) return value
  
  return formatFieldConfig(value, config)
}

/**
 * 限制数字输入
 * @param {String} value 输入值
 * @param {Object} options 选项
 * @returns {String} 处理后的值
 */
export function limitNumberInput(value, options = {}) {
  const { precision = 2, min, max, allowNegative = false } = options
  
  if (typeof value !== 'string') return value
  
  // 移除非数字字符
  let cleanValue = value.replace(/[^\d.-]/g, '')
  
  // 处理负号
  if (!allowNegative) {
    cleanValue = cleanValue.replace(/-/g, '')
  } else {
    // 只允许开头有一个负号
    const parts = cleanValue.split('-')
    if (parts.length > 2) {
      cleanValue = '-' + parts.slice(1).join('')
    }
  }
  
  // 处理小数点
  const dotIndex = cleanValue.indexOf('.')
  if (dotIndex !== -1) {
    const beforeDot = cleanValue.substring(0, dotIndex)
    const afterDot = cleanValue.substring(dotIndex + 1)
    
    // 限制小数位数
    const limitedAfterDot = afterDot.substring(0, precision)
    cleanValue = beforeDot + '.' + limitedAfterDot
  }
  
  // 转换为数字进行范围检查
  const num = Number(cleanValue)
  if (!isNaN(num)) {
    if (min !== undefined && num < min) {
      return String(min)
    }
    if (max !== undefined && num > max) {
      return String(max)
    }
  }
  
  return cleanValue
}

/**
 * 格式化金额显示
 * @param {Number} amount 金额
 * @param {Object} options 选项
 * @returns {String} 格式化后的金额
 */
export function formatAmount(amount, options = {}) {
  const { 
    precision = 2, 
    unit = '万元', 
    showUnit = true, 
    thousandSeparator = true 
  } = options
  
  if (amount === null || amount === undefined || amount === '') {
    return ''
  }
  
  const num = Number(amount)
  if (isNaN(num)) {
    return amount
  }
  
  let formatted = num.toFixed(precision)
  
  // 添加千分位分隔符
  if (thousandSeparator) {
    const parts = formatted.split('.')
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    formatted = parts.join('.')
  }
  
  return showUnit ? `${formatted} ${unit}` : formatted
}

/**
 * 格式化面积显示
 * @param {Number} area 面积
 * @param {Object} options 选项
 * @returns {String} 格式化后的面积
 */
export function formatArea(area, options = {}) {
  const { precision = 2, unit = '㎡', showUnit = true } = options
  
  if (area === null || area === undefined || area === '') {
    return ''
  }
  
  const num = Number(area)
  if (isNaN(num)) {
    return area
  }
  
  const formatted = num.toFixed(precision)
  return showUnit ? `${formatted} ${unit}` : formatted
}

/**
 * 验证项目编码格式
 * @param {String} code 项目编码
 * @returns {Boolean} 是否有效
 */
export function validateProjectCode(code) {
  if (!code) return false
  
  // 项目编码格式：字母开头，可包含字母、数字、连字符
  const pattern = /^[A-Za-z][A-Za-z0-9-]*$/
  return pattern.test(code)
}

/**
 * 验证建筑面积
 * @param {Number} area 建筑面积
 * @returns {Object} 验证结果
 */
export function validateBuildingArea(area) {
  if (area === null || area === undefined || area === '') {
    return { valid: false, message: '建筑面积不能为空' }
  }
  
  const num = Number(area)
  if (isNaN(num)) {
    return { valid: false, message: '建筑面积必须为数字' }
  }
  
  if (num <= 0) {
    return { valid: false, message: '建筑面积必须大于0' }
  }
  
  if (num > 1000000) {
    return { valid: false, message: '建筑面积不能超过100万平方米' }
  }
  
  return { valid: true }
}

/**
 * 生成唯一ID
 * @param {String} prefix 前缀
 * @returns {String} 唯一ID
 */
export function generateUniqueId(prefix = 'id') {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8)
  return `${prefix}_${timestamp}_${random}`
}

/**
 * 深度克隆对象
 * @param {Any} obj 要克隆的对象
 * @returns {Any} 克隆后的对象
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime())
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item))
  }
  
  if (typeof obj === 'object') {
    const cloned = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key])
      }
    }
    return cloned
  }
  
  return obj
}

/**
 * 防抖函数
 * @param {Function} func 要防抖的函数
 * @param {Number} delay 延迟时间
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, delay = 300) {
  let timeoutId
  
  return function (...args) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * 节流函数
 * @param {Function} func 要节流的函数
 * @param {Number} delay 延迟时间
 * @returns {Function} 节流后的函数
 */
export function throttle(func, delay = 300) {
  let lastCall = 0
  
  return function (...args) {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      return func.apply(this, args)
    }
  }
}

/**
 * 检查对象是否为空
 * @param {Object} obj 要检查的对象
 * @returns {Boolean} 是否为空
 */
export function isEmpty(obj) {
  if (obj === null || obj === undefined) return true
  if (typeof obj === 'string') return obj.trim() === ''
  if (Array.isArray(obj)) return obj.length === 0
  if (typeof obj === 'object') return Object.keys(obj).length === 0
  return false
}

/**
 * 安全的JSON解析
 * @param {String} jsonString JSON字符串
 * @param {Any} defaultValue 默认值
 * @returns {Any} 解析结果
 */
export function safeJsonParse(jsonString, defaultValue = null) {
  try {
    return JSON.parse(jsonString)
  } catch (error) {
    console.warn('JSON解析失败:', error)
    return defaultValue
  }
}

/**
 * 安全的JSON字符串化
 * @param {Any} obj 要字符串化的对象
 * @param {String} defaultValue 默认值
 * @returns {String} JSON字符串
 */
export function safeJsonStringify(obj, defaultValue = '{}') {
  try {
    return JSON.stringify(obj)
  } catch (error) {
    console.warn('JSON字符串化失败:', error)
    return defaultValue
  }
}

/**
 * 获取嵌套对象属性值
 * @param {Object} obj 对象
 * @param {String} path 属性路径，如 'a.b.c'
 * @param {Any} defaultValue 默认值
 * @returns {Any} 属性值
 */
export function getNestedValue(obj, path, defaultValue = undefined) {
  if (!obj || !path) return defaultValue
  
  const keys = path.split('.')
  let current = obj
  
  for (const key of keys) {
    if (current === null || current === undefined || !(key in current)) {
      return defaultValue
    }
    current = current[key]
  }
  
  return current
}

/**
 * 设置嵌套对象属性值
 * @param {Object} obj 对象
 * @param {String} path 属性路径，如 'a.b.c'
 * @param {Any} value 要设置的值
 */
export function setNestedValue(obj, path, value) {
  if (!obj || !path) return
  
  const keys = path.split('.')
  let current = obj
  
  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i]
    if (!(key in current) || typeof current[key] !== 'object') {
      current[key] = {}
    }
    current = current[key]
  }
  
  current[keys[keys.length - 1]] = value
}
