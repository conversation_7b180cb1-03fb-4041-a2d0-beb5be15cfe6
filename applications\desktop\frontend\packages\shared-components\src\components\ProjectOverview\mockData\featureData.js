/**
 * 工程特征模拟数据
 * 参考原有项目的数据结构和字段
 */

// 工程特征模拟数据
export const mockFeatureData = [
  // 结构特征分组
  {
    sequenceNbr: 1,
    dispNo: '1',
    name: '结构特征',
    context: '',
    addFlag: 0,
    lockFlag: 0,
    type: 'title',
    groupCode: 1,
    parentId: null,
    children: [
      {
        sequenceNbr: 2,
        dispNo: '1.1',
        name: '结构类型',
        context: '框架结构',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        children: [],
        jsonStr: ['框架结构', '剪力墙结构', '框剪结构', '砖混结构', '钢结构']
      },
      {
        sequenceNbr: 3,
        dispNo: '1.2',
        name: '基础类型',
        context: '筏板基础',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        children: [],
        jsonStr: ['独立基础', '条形基础', '筏板基础', '桩基础', '其他']
      },
      {
        sequenceNbr: 4,
        dispNo: '1.3',
        name: '抗震设防烈度',
        context: '7度',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        children: [],
        jsonStr: ['6度', '7度', '8度', '9度']
      },
      {
        sequenceNbr: 5,
        dispNo: '1.4',
        name: '建筑层数',
        context: '18层',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        children: []
      },
      {
        sequenceNbr: 6,
        dispNo: '1.5',
        name: '建筑高度',
        context: '54.0m',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        children: []
      },
      {
        sequenceNbr: 7,
        dispNo: '1.6',
        name: '标准层层高',
        context: '3.0m',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 1,
        parentId: 1,
        children: []
      }
    ]
  },

  // 建筑特征分组
  {
    sequenceNbr: 8,
    dispNo: '2',
    name: '建筑特征',
    context: '',
    addFlag: 0,
    lockFlag: 0,
    type: 'title',
    groupCode: 2,
    parentId: null,
    children: [
      {
        sequenceNbr: 9,
        dispNo: '2.1',
        name: '建筑功能',
        context: '住宅',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 2,
        parentId: 8,
        children: [],
        jsonStr: ['住宅', '办公', '商业', '工业', '教育', '医疗', '其他']
      },
      {
        sequenceNbr: 10,
        dispNo: '2.2',
        name: '建筑等级',
        context: '二级',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 2,
        parentId: 8,
        children: [],
        jsonStr: ['一级', '二级', '三级', '四级']
      },
      {
        sequenceNbr: 11,
        dispNo: '2.3',
        name: '耐火等级',
        context: '一级',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 2,
        parentId: 8,
        children: [],
        jsonStr: ['一级', '二级', '三级', '四级']
      },
      {
        sequenceNbr: 12,
        dispNo: '2.4',
        name: '屋面类型',
        context: '平屋面',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 2,
        parentId: 8,
        children: [],
        jsonStr: ['平屋面', '坡屋面', '种植屋面', '其他']
      }
    ]
  },

  // 装修特征分组
  {
    sequenceNbr: 13,
    dispNo: '3',
    name: '装修特征',
    context: '',
    addFlag: 0,
    lockFlag: 0,
    type: 'title',
    groupCode: 3,
    parentId: null,
    children: [
      {
        sequenceNbr: 14,
        dispNo: '3.1',
        name: '装修标准',
        context: '精装',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 3,
        parentId: 13,
        children: [],
        jsonStr: ['毛坯', '简装', '精装', '豪装']
      },
      {
        sequenceNbr: 15,
        dispNo: '3.2',
        name: '外墙装修',
        context: '面砖',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 3,
        parentId: 13,
        children: [],
        jsonStr: ['涂料', '面砖', '石材', '玻璃幕墙', '金属幕墙']
      },
      {
        sequenceNbr: 16,
        dispNo: '3.3',
        name: '内墙装修',
        context: '乳胶漆',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 3,
        parentId: 13,
        children: [],
        jsonStr: ['乳胶漆', '壁纸', '瓷砖', '石材', '其他']
      },
      {
        sequenceNbr: 17,
        dispNo: '3.4',
        name: '地面装修',
        context: '瓷砖',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 3,
        parentId: 13,
        children: [],
        jsonStr: ['瓷砖', '木地板', '石材', '地毯', '其他']
      },
      {
        sequenceNbr: 18,
        dispNo: '3.5',
        name: '天棚装修',
        context: '乳胶漆',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 3,
        parentId: 13,
        children: [],
        jsonStr: ['乳胶漆', '石膏板吊顶', '铝扣板吊顶', '其他']
      }
    ]
  },

  // 设备特征分组
  {
    sequenceNbr: 19,
    dispNo: '4',
    name: '设备特征',
    context: '',
    addFlag: 0,
    lockFlag: 0,
    type: 'title',
    groupCode: 4,
    parentId: null,
    children: [
      {
        sequenceNbr: 20,
        dispNo: '4.1',
        name: '电梯配置',
        context: '客梯2台，载重1000kg',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 4,
        parentId: 19,
        children: []
      },
      {
        sequenceNbr: 21,
        dispNo: '4.2',
        name: '空调系统',
        context: '分体空调',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 4,
        parentId: 19,
        children: [],
        jsonStr: ['中央空调', '分体空调', '多联机', '无空调']
      },
      {
        sequenceNbr: 22,
        dispNo: '4.3',
        name: '消防系统',
        context: '自动喷淋系统、火灾自动报警系统',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 4,
        parentId: 19,
        children: []
      },
      {
        sequenceNbr: 23,
        dispNo: '4.4',
        name: '智能化系统',
        context: '楼宇对讲、监控系统、门禁系统',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 4,
        parentId: 19,
        children: []
      },
      {
        sequenceNbr: 24,
        dispNo: '4.5',
        name: '给排水系统',
        context: '生活给水、污水排水、雨水排水',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 4,
        parentId: 19,
        children: []
      }
    ]
  },

  // 环境特征分组
  {
    sequenceNbr: 25,
    dispNo: '5',
    name: '环境特征',
    context: '',
    addFlag: 0,
    lockFlag: 0,
    type: 'title',
    groupCode: 5,
    parentId: null,
    children: [
      {
        sequenceNbr: 26,
        dispNo: '5.1',
        name: '绿化率',
        context: '35%',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 5,
        parentId: 25,
        children: []
      },
      {
        sequenceNbr: 27,
        dispNo: '5.2',
        name: '容积率',
        context: '2.5',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 5,
        parentId: 25,
        children: []
      },
      {
        sequenceNbr: 28,
        dispNo: '5.3',
        name: '建筑密度',
        context: '25%',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 5,
        parentId: 25,
        children: []
      },
      {
        sequenceNbr: 29,
        dispNo: '5.4',
        name: '停车位配置',
        context: '地下车库150个车位',
        addFlag: 0,
        lockFlag: 0,
        type: 'item',
        groupCode: 5,
        parentId: 25,
        children: []
      }
    ]
  }
]

// 根据模块类型获取特定的工程特征数据
export function getFeatureDataByModule(moduleType) {
  const baseData = JSON.parse(JSON.stringify(mockFeatureData))
  
  // 根据不同模块调整数据
  switch (moduleType) {
    case 'budget':
      // 预算模块可能需要更详细的特征
      break
      
    case 'settlement':
      // 结算模块可能关注实际完成情况
      baseData.push({
        sequenceNbr: 30,
        dispNo: '6',
        name: '变更情况',
        context: '',
        addFlag: 0,
        lockFlag: 0,
        type: 'title',
        groupCode: 6,
        parentId: null,
        children: [
          {
            sequenceNbr: 31,
            dispNo: '6.1',
            name: '设计变更',
            context: '外墙材料由面砖变更为涂料',
            addFlag: 0,
            lockFlag: 0,
            type: 'item',
            groupCode: 6,
            parentId: 30,
            children: []
          },
          {
            sequenceNbr: 32,
            dispNo: '6.2',
            name: '现场签证',
            context: '增加地下室防水处理',
            addFlag: 0,
            lockFlag: 0,
            type: 'item',
            groupCode: 6,
            parentId: 30,
            children: []
          }
        ]
      })
      break
      
    case 'review':
      // 审核模块可能不需要显示工程特征
      return []
      
    case 'material':
      // 工料机模块可能不需要显示工程特征
      return []
  }
  
  return baseData
}
