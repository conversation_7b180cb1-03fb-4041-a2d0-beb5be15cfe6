<template>
  <div class="tab-page-content cost-analysis">
    <div class="page-header">
      <h3>造价分析</h3>
      <div class="page-actions">
        <a-button @click="handleAnalyze">
          <template #icon><BarChartOutlined /></template>
          重新分析
        </a-button>
        <a-button @click="handleExport">
          <template #icon><ExportOutlined /></template>
          导出报告
        </a-button>
      </div>
    </div>

    <div class="page-body">
      <!-- 造价总览 -->
      <a-card title="造价总览" class="overview-card">
        <a-row :gutter="24">
          <a-col :span="8">
            <a-statistic
              :title="`总${amountLabel}`"
              :value="analysisData.totalAmount"
              :precision="2"
              suffix="万元"
              :value-style="{ color: '#cf1322', fontSize: '24px' }"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="单位面积造价"
              :value="analysisData.unitAreaCost"
              :precision="2"
              suffix="元/㎡"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="8">
            <a-statistic
              title="建筑面积"
              :value="analysisData.buildingArea"
              suffix="㎡"
              :value-style="{ color: '#52c41a' }"
            />
          </a-col>
        </a-row>
      </a-card>

      <!-- 费用构成分析 -->
      <a-row :gutter="16">
        <a-col :span="12">
          <a-card title="费用构成分析" class="composition-card">
            <div class="chart-container">
              <div class="chart-placeholder">
                <a-empty
                  description="图表组件开发中..."
                  :image="Empty.PRESENTED_IMAGE_SIMPLE"
                />
              </div>
            </div>

            <!-- 费用构成表格 -->
            <a-table
              :data-source="compositionData"
              :columns="compositionColumns"
              :pagination="false"
              size="small"
              class="composition-table"
            >
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'percentage'">
                  <a-progress
                    :percent="record.percentage"
                    size="small"
                    :stroke-color="record.color"
                  />
                </template>
              </template>
            </a-table>
          </a-card>
        </a-col>

        <a-col :span="12">
          <a-card title="造价指标对比" class="comparison-card">
            <div class="comparison-content">
              <div class="indicator-group">
                <h4>人材机占比</h4>
                <a-row :gutter="16">
                  <a-col :span="8">
                    <div class="indicator-item">
                      <div class="indicator-value" style="color: #3f8600;">
                        {{ analysisData.laborPercentage }}%
                      </div>
                      <div class="indicator-label">人工费</div>
                    </div>
                  </a-col>
                  <a-col :span="8">
                    <div class="indicator-item">
                      <div class="indicator-value" style="color: #1890ff;">
                        {{ analysisData.materialPercentage }}%
                      </div>
                      <div class="indicator-label">材料费</div>
                    </div>
                  </a-col>
                  <a-col :span="8">
                    <div class="indicator-item">
                      <div class="indicator-value" style="color: #722ed1;">
                        {{ analysisData.mechanicalPercentage }}%
                      </div>
                      <div class="indicator-label">机械费</div>
                    </div>
                  </a-col>
                </a-row>
              </div>

              <a-divider />

              <div class="indicator-group">
                <h4>行业对比</h4>
                <a-row :gutter="16">
                  <a-col :span="12">
                    <a-statistic
                      title="行业平均单价"
                      :value="analysisData.industryAverage"
                      :precision="2"
                      suffix="元/㎡"
                    />
                  </a-col>
                  <a-col :span="12">
                    <a-statistic
                      title="偏差率"
                      :value="analysisData.deviationRate"
                      :precision="1"
                      suffix="%"
                      :value-style="{ color: analysisData.deviationRate > 0 ? '#cf1322' : '#3f8600' }"
                    />
                  </a-col>
                </a-row>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 详细分析表 -->
      <a-card title="分项造价分析" class="detail-analysis-card">
        <a-table
          :data-source="detailAnalysisData"
          :columns="detailAnalysisColumns"
          :scroll="{ x: 1000 }"
          size="small"
        />
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { BarChartOutlined, ExportOutlined } from '@ant-design/icons-vue'
import { Empty } from 'ant-design-vue'

const props = defineProps({
  moduleType: {
    type: String,
    required: true
  },
  levelType: {
    type: Number,
    required: true
  },
  tabInfo: {
    type: Object,
    default: () => ({})
  },
  projectData: {
    type: Object,
    default: () => ({})
  },
  tableData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 分析数据
const analysisData = ref({
  totalAmount: 0,
  unitAreaCost: 0,
  buildingArea: 10000,
  laborPercentage: 0,
  materialPercentage: 0,
  mechanicalPercentage: 0,
  industryAverage: 3500,
  deviationRate: 0
})

// 费用构成数据
const compositionData = ref([])

// 详细分析数据
const detailAnalysisData = ref([])

// 计算属性
const amountLabel = computed(() => {
  const labels = {
    'budget': '预算金额',
    'rough-estimate': '概算金额',
    'settlement': '结算金额',
    'material': '工料机金额'
  }
  return labels[props.moduleType] || '金额'
})

// 费用构成表格列
const compositionColumns = [
  {
    title: '费用类别',
    dataIndex: 'category',
    key: 'category',
    width: 120
  },
  {
    title: '金额(万元)',
    dataIndex: 'amount',
    key: 'amount',
    width: 100,
    align: 'right',
    customRender: ({ text }) => Number(text).toFixed(2)
  },
  {
    title: '占比',
    dataIndex: 'percentage',
    key: 'percentage',
    width: 200
  }
]

// 详细分析表格列
const detailAnalysisColumns = [
  {
    title: '项目分类',
    dataIndex: 'category',
    key: 'category',
    width: 150
  },
  {
    title: '数量',
    dataIndex: 'quantity',
    key: 'quantity',
    width: 100,
    align: 'right'
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 80,
    align: 'center'
  },
  {
    title: '单价(元)',
    dataIndex: 'unitPrice',
    key: 'unitPrice',
    width: 120,
    align: 'right',
    customRender: ({ text }) => Number(text || 0).toFixed(2)
  },
  {
    title: '合价(万元)',
    dataIndex: 'totalPrice',
    key: 'totalPrice',
    width: 120,
    align: 'right',
    customRender: ({ text }) => Number(text || 0).toFixed(2)
  },
  {
    title: '占总造价比例',
    dataIndex: 'percentage',
    key: 'percentage',
    width: 120,
    align: 'right',
    customRender: ({ text }) => `${Number(text || 0).toFixed(1)}%`
  },
  {
    title: '备注',
    dataIndex: 'remark',
    key: 'remark'
  }
]

// 事件处理
const handleAnalyze = () => {
  performAnalysis()
  message.success('造价分析完成')
  emit('action', 'analyze', analysisData.value)
}

const handleExport = () => {
  emit('action', 'export', {
    type: 'cost-analysis',
    data: {
      analysis: analysisData.value,
      composition: compositionData.value,
      details: detailAnalysisData.value
    }
  })
  message.success('分析报告导出成功')
}

// 执行造价分析
const performAnalysis = () => {
  if (!props.tableData || props.tableData.length === 0) {
    return
  }

  // 扁平化数据
  const flattenData = (items) => {
    let result = []
    items.forEach(item => {
      result.push(item)
      if (item.children && item.children.length > 0) {
        result = result.concat(flattenData(item.children))
      }
    })
    return result
  }

  const allItems = flattenData(props.tableData)

  // 计算总金额
  const totalAmount = allItems.reduce((sum, item) => sum + (item.amount || 0), 0)

  // 计算人材机费用
  const laborCost = allItems.reduce((sum, item) => sum + (item.laborCost || 0), 0)
  const materialCost = allItems.reduce((sum, item) => sum + (item.materialCost || 0), 0)
  const mechanicalCost = allItems.reduce((sum, item) => sum + (item.mechanicalCost || 0), 0)

  // 更新分析数据
  analysisData.value = {
    totalAmount: totalAmount / 10000, // 转换为万元
    unitAreaCost: totalAmount / analysisData.value.buildingArea,
    buildingArea: analysisData.value.buildingArea,
    laborPercentage: totalAmount > 0 ? (laborCost / totalAmount * 100).toFixed(1) : 0,
    materialPercentage: totalAmount > 0 ? (materialCost / totalAmount * 100).toFixed(1) : 0,
    mechanicalPercentage: totalAmount > 0 ? (mechanicalCost / totalAmount * 100).toFixed(1) : 0,
    industryAverage: 3500,
    deviationRate: totalAmount > 0 ? (((totalAmount / analysisData.value.buildingArea) - 3500) / 3500 * 100).toFixed(1) : 0
  }

  // 更新费用构成数据
  compositionData.value = [
    {
      key: '1',
      category: '人工费',
      amount: (laborCost / 10000).toFixed(2),
      percentage: Number(analysisData.value.laborPercentage),
      color: '#3f8600'
    },
    {
      key: '2',
      category: '材料费',
      amount: (materialCost / 10000).toFixed(2),
      percentage: Number(analysisData.value.materialPercentage),
      color: '#1890ff'
    },
    {
      key: '3',
      category: '机械费',
      amount: (mechanicalCost / 10000).toFixed(2),
      percentage: Number(analysisData.value.mechanicalPercentage),
      color: '#722ed1'
    },
    {
      key: '4',
      category: '其他费用',
      amount: ((totalAmount - laborCost - materialCost - mechanicalCost) / 10000).toFixed(2),
      percentage: totalAmount > 0 ? ((totalAmount - laborCost - materialCost - mechanicalCost) / totalAmount * 100).toFixed(1) : 0,
      color: '#faad14'
    }
  ]

  // 更新详细分析数据
  const categoryMap = {}
  allItems.forEach(item => {
    if (!item.type) return

    if (!categoryMap[item.type]) {
      categoryMap[item.type] = {
        category: getCategoryName(item.type),
        quantity: 0,
        unit: item.unit || '项',
        totalPrice: 0,
        items: []
      }
    }

    categoryMap[item.type].quantity += item.quantity || 0
    categoryMap[item.type].totalPrice += item.amount || 0
    categoryMap[item.type].items.push(item)
  })

  detailAnalysisData.value = Object.values(categoryMap).map(category => ({
    category: category.category,
    quantity: category.quantity.toFixed(2),
    unit: category.unit,
    unitPrice: category.quantity > 0 ? (category.totalPrice / category.quantity).toFixed(2) : 0,
    totalPrice: (category.totalPrice / 10000).toFixed(2),
    percentage: totalAmount > 0 ? (category.totalPrice / totalAmount * 100).toFixed(1) : 0,
    remark: `包含${category.items.length}个子项`
  }))
}

// 获取分类名称
const getCategoryName = (type) => {
  const typeMap = {
    'foundation': '基础工程',
    'structure': '主体结构',
    'decoration': '装修工程',
    'installation': '安装工程',
    'earthwork': '土方工程',
    'concrete': '混凝土工程',
    'steel': '钢筋工程',
    'formwork': '模板工程',
    'road': '道路工程',
    'drainage': '排水工程',
    'landscape': '绿化工程',
    'safety': '安全工程',
    'technology': '智能化工程'
  }
  return typeMap[type] || type
}

// 监听数据变化
watch(() => props.tableData, () => {
  performAnalysis()
}, { deep: true })

onMounted(() => {
  performAnalysis()
})
</script>

<style lang="scss" scoped>
.cost-analysis {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      color: #333;
      font-weight: 600;
    }

    .page-actions {
      display: flex;
      gap: 8px;
    }
  }

  .page-body {
    flex: 1;
    overflow-y: auto;

    .overview-card {
      margin-bottom: 16px;

      :deep(.ant-statistic-content) {
        font-weight: 600;
      }
    }

    .composition-card,
    .comparison-card,
    .detail-analysis-card {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .chart-container {
      height: 200px;
      margin-bottom: 16px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;

      .chart-placeholder {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
      }
    }

    .composition-table {
      :deep(.ant-table-tbody > tr > td) {
        padding: 8px 16px;
      }
    }

    .comparison-content {
      .indicator-group {
        h4 {
          margin-bottom: 12px;
          color: #333;
          font-size: 14px;
        }

        .indicator-item {
          text-align: center;

          .indicator-value {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
          }

          .indicator-label {
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
  }
}
</style>