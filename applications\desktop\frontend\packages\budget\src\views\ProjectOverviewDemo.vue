<template>
  <div class="project-overview-demo">
    <div class="demo-header">
      <h2>项目概况模块演示</h2>
      <p>展示基于原有项目样式和功能的新共享组件</p>
    </div>
    
    <div class="demo-content">
      <a-card title="预算模块 - 项目概况" class="demo-card">
        <ProjectOverview
          module-type="budget"
          :level-type="3"
          :project-data="{}"
          :tab-info="{ code: 'project-overview', name: '项目概况' }"
          :editable="true"
          :custom-config="{}"
          :permissions="{
            canEdit: true,
            canDelete: true,
            canLock: true,
            canExport: true
          }"
          @data-change="handleDataChange"
          @save="handleSave"
          @export="handleExport"
          @lock="handleLock"
        />
      </a-card>
    </div>
    
    <div class="demo-info">
      <a-card title="功能说明" size="small">
        <ul>
          <li><strong>基本信息</strong>: 包含项目基本信息、工程所在地、招标信息、投标信息等分组</li>
          <li><strong>工程特征</strong>: 包含结构特征、建筑特征、装修特征、设备特征、环境特征等分组</li>
          <li><strong>模拟数据</strong>: 基于原有项目的数据结构创建的完整模拟数据</li>
          <li><strong>样式还原</strong>: 参考原有项目的样式设计，保持一致的用户体验</li>
          <li><strong>模块差异化</strong>: 支持不同模块的特有字段和配置</li>
        </ul>
      </a-card>
      
      <a-card title="操作说明" size="small" style="margin-top: 16px;">
        <ul>
          <li><strong>编辑</strong>: 双击表格单元格进行编辑</li>
          <li><strong>右键菜单</strong>: 右键点击行可显示操作菜单</li>
          <li><strong>保存</strong>: 点击保存按钮保存当前数据</li>
          <li><strong>导出</strong>: 点击导出按钮导出项目概况</li>
          <li><strong>锁定</strong>: 点击锁定按钮锁定/解锁编辑</li>
        </ul>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { ProjectOverview } from '@cost-app/shared-components'

// 事件处理
const handleDataChange = (data) => {
  console.log('演示页面收到数据变更:', data)
  
  // 可以在这里处理数据变更的全局逻辑
  if (data.type === 'basic') {
    console.log('基本信息变更:', data.data)
  } else if (data.type === 'feature') {
    console.log('工程特征变更:', data.data)
  }
}

const handleSave = (data) => {
  console.log('保存数据:', data)
  message.success('演示模式：数据已保存到控制台')
}

const handleExport = (data) => {
  console.log('导出数据:', data)
  message.success('演示模式：数据已导出到控制台')
}

const handleLock = (lockStatus) => {
  console.log('锁定状态:', lockStatus)
  message.success(`演示模式：${lockStatus ? '已锁定' : '已解锁'}`)
}
</script>

<style lang="scss" scoped>
.project-overview-demo {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  
  .demo-header {
    padding: 24px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    text-align: center;
    
    h2 {
      margin: 0 0 8px 0;
      color: #262626;
      font-size: 24px;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: #8c8c8c;
      font-size: 14px;
    }
  }
  
  .demo-content {
    flex: 1;
    padding: 24px;
    overflow: hidden;
    
    .demo-card {
      height: 100%;
      
      :deep(.ant-card-body) {
        height: calc(100% - 57px);
        padding: 0;
      }
    }
  }
  
  .demo-info {
    padding: 0 24px 24px 24px;
    
    .ant-card {
      :deep(.ant-card-body) {
        padding: 16px;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          line-height: 1.5;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          strong {
            color: #262626;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .project-overview-demo {
    .demo-content {
      padding: 16px;
    }
    
    .demo-info {
      padding: 0 16px 16px 16px;
    }
  }
}

@media (max-width: 768px) {
  .project-overview-demo {
    .demo-header {
      padding: 16px;
      
      h2 {
        font-size: 20px;
      }
    }
    
    .demo-content {
      padding: 12px;
    }
    
    .demo-info {
      padding: 0 12px 12px 12px;
      
      .ant-card {
        margin-top: 12px !important;
      }
    }
  }
}
</style>
