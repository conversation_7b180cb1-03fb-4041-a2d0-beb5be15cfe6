<template>
  <div class="tab-container">
    <!-- Tab导航 -->
    <div class="tab-navigation">
      <TabMenu
        ref="tabMenuRef"
        :level-type="levelType"
        :module-type="moduleType"
        :is-first-level-child="isFirstLevelChild"
        :is-standard-group="isStandardGroup"
        :has-professional-project="hasProfessionalProject"
        :class="`${moduleType}-theme`"
        @tab-change="handleTabChange"
        @get-title="handleTitleChange"
      />
    </div>

    <!-- Tab内容区域 -->
    <div class="tab-content-area">
      <component
        :is="currentTabComponent"
        v-if="currentTabComponent"
        v-bind="tabProps"
        @data-change="handleDataChange"
        @action="handleAction"
      />

      <!-- 默认占位内容 -->
      <div v-else class="tab-placeholder">
        <a-empty
          :description="`${currentTab?.value || '当前Tab'} 页面开发中...`"
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Empty } from 'ant-design-vue'
import TabMenu from './TabMenu.vue'

const props = defineProps({
  // Tab组件映射配置
  tabComponents: {
    type: Object,
    required: true,
    default: () => ({})
    // 示例: { '1': 'ProjectOverview', '4': 'ItemDetail' }
  },
  // 模块类型
  moduleType: {
    type: String,
    required: true
  },
  // 层级类型
  levelType: {
    type: Number,
    required: true
  },
  // 项目数据
  projectData: {
    type: Object,
    default: () => ({})
  },
  // 表格数据
  tableData: {
    type: Array,
    default: () => []
  },
  // 权限配置
  isFirstLevelChild: {
    type: Boolean,
    default: true
  },
  isStandardGroup: {
    type: Boolean,
    default: false
  },
  hasProfessionalProject: {
    type: Boolean,
    default: true
  },
  // 传递给Tab组件的额外属性
  tabProps: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['tabChange', 'titleChange', 'dataChange', 'action'])

// 当前选中的Tab信息
const currentTab = ref({})
const tabMenuRef = ref()

// 当前Tab对应的组件
const currentTabComponent = computed(() => {
  const tabCode = currentTab.value?.code
  if (!tabCode) return null

  return props.tabComponents[tabCode] || null
})

// 传递给Tab组件的属性
const computedTabProps = computed(() => ({
  moduleType: props.moduleType,
  levelType: props.levelType,
  tabInfo: currentTab.value,
  projectData: props.projectData,
  tableData: props.tableData,
  isFirstLevelChild: props.isFirstLevelChild,
  isStandardGroup: props.isStandardGroup,
  hasProfessionalProject: props.hasProfessionalProject,
  ...props.tabProps
}))

// Tab切换处理
const handleTabChange = (tabData) => {
  currentTab.value = tabData
  emit('tabChange', tabData)
}

// 标题变化处理
const handleTitleChange = (title) => {
  emit('titleChange', title)
}

// 数据变化处理
const handleDataChange = (data) => {
  emit('dataChange', {
    tab: currentTab.value,
    data
  })
}

// 操作事件处理
const handleAction = (action, data) => {
  emit('action', {
    tab: currentTab.value,
    action,
    data
  })
}

// 暴露方法给父组件
defineExpose({
  // 切换到指定Tab
  switchTab: (tabCode) => {
    return tabMenuRef.value?.changeTabByCode?.(tabCode)
  },
  // 根据名称切换Tab
  switchTabByName: (tabName) => {
    return tabMenuRef.value?.changeTabByName?.(tabName)
  },
  // 获取当前Tab信息
  getCurrentTab: () => {
    return currentTab.value
  },
  // 获取TabMenu引用
  getTabMenu: () => {
    return tabMenuRef.value
  }
})
</script>

<style lang="scss" scoped>
.tab-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .tab-navigation {
    flex-shrink: 0;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
  }

  .tab-content-area {
    flex: 1;
    overflow: hidden;
    background: #f5f5f5;

    .tab-placeholder {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fafafa;
    }
  }
}
</style>