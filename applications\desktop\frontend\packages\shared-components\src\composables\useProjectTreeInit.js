import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 项目树初始化管理 composable
 * 用于统一管理所有模块的项目树初始化逻辑
 */
export function useProjectTreeInit() {

  /**
   * 标准的项目树结构 - 项目-单项-单位三级结构
   * 适用于预算、概算、结算、审查等所有业务模块
   */
  const getStandardProjectTree = () => [
    {
      key: 'main-project',
      title: '综合办公楼建设项目',
      type: 'project',
      level: 1,
      children: [
        {
          key: 'building-single',
          title: '主体建筑单项',
          type: 'single',
          level: 2,
          children: [
            { key: 'structure-unit', title: '主体结构单位工程', type: 'unit', level: 3 },
            { key: 'decoration-unit', title: '建筑装饰单位工程', type: 'unit', level: 3 },
            { key: 'water-unit', title: '给排水单位工程', type: 'unit', level: 3 },
            { key: 'electric-unit', title: '电气工程单位工程', type: 'unit', level: 3 },
            { key: 'hvac-unit', title: '暖通空调单位工程', type: 'unit', level: 3 }
          ]
        },
        {
          key: 'infrastructure-single',
          title: '配套设施单项',
          type: 'single',
          level: 2,
          children: [
            { key: 'road-unit', title: '道路工程单位工程', type: 'unit', level: 3 },
            { key: 'parking-unit', title: '停车场单位工程', type: 'unit', level: 3 },
            { key: 'landscape-unit', title: '景观绿化单位工程', type: 'unit', level: 3 },
            { key: 'security-unit', title: '安防系统单位工程', type: 'unit', level: 3 }
          ]
        },
        {
          key: 'utility-single',
          title: '公用工程单项',
          type: 'single',
          level: 2,
          children: [
            { key: 'power-unit', title: '供配电单位工程', type: 'unit', level: 3 },
            { key: 'water-supply-unit', title: '供水系统单位工程', type: 'unit', level: 3 },
            { key: 'sewage-unit', title: '污水处理单位工程', type: 'unit', level: 3 },
            { key: 'gas-unit', title: '燃气工程单位工程', type: 'unit', level: 3 }
          ]
        }
      ]
    }
  ]

  /**
   * 获取默认的项目树配置
   * @param {string} moduleType - 模块类型 (budget/rough-estimate/settlement/audit)
   * @returns {Object} 项目树配置对象
   */
  const getDefaultProjectTreeConfig = (moduleType = 'budget') => {
    const projectTree = getStandardProjectTree()

    return {
      // 项目树数据
      projectTree: ref(projectTree),
      // 默认选中项目级别的节点
      selectedProjectKeys: ref(['main-project']),
      // 默认展开项目和第一个单项
      expandedProjectKeys: ref(['main-project', 'building-single']),
      // 当前选中的项目节点
      currentProject: ref(null),
      // 当前层级（默认项目级别）
      currentLevel: ref(1)
    }
  }

  /**
   * 初始化项目树状态
   * @param {Object} config - 项目树配置对象
   * @param {string} moduleType - 模块类型
   */
  const initializeProjectTree = (config, moduleType = 'budget') => {
    // 查找默认选中的项目节点
    const projectNode = config.projectTree.value.find(node => node.key === 'main-project')
    if (projectNode) {
      config.currentProject.value = projectNode
      config.currentLevel.value = 1  // 默认选中项目级别

      const moduleNames = {
        'budget': '预算',
        'rough-estimate': '概算',
        'settlement': '结算',
        'audit': '审查',
        'material': '工料机'
      }

      const moduleName = moduleNames[moduleType] || moduleType
      message.success(`${moduleName}模块已加载，当前选择: ${projectNode.title}`)
    }
  }

  /**
   * 项目树选择事件处理
   * @param {Array} selectedKeys - 选中的键值数组
   * @param {Object} info - 选择信息对象
   * @param {Object} config - 项目树配置对象
   */
  const handleProjectSelect = (selectedKeys, info, config) => {
    config.selectedProjectKeys.value = selectedKeys
    if (selectedKeys.length > 0) {
      const node = info.node.dataRef || info.node
      config.currentProject.value = node

      // 根据选中的节点类型，设置对应的层级
      if (node.type === 'project') {
        // 项目层级
        config.currentLevel.value = 1
        message.info(`选择项目: ${node.title}`)
      } else if (node.type === 'single') {
        // 单项层级
        config.currentLevel.value = 2
        message.info(`选择单项工程: ${node.title}`)
      } else if (node.type === 'unit') {
        // 单位层级
        config.currentLevel.value = 3
        message.info(`选择单位工程: ${node.title}`)
      }
    }
  }

  /**
   * 项目树展开事件处理
   * @param {Array} expandedKeys - 展开的键值数组
   * @param {Object} config - 项目树配置对象
   */
  const handleProjectExpand = (expandedKeys, config) => {
    config.expandedProjectKeys.value = expandedKeys
  }

  /**
   * 项目树刷新处理
   * @param {string} moduleType - 模块类型
   */
  const handleProjectRefresh = (moduleType = 'budget') => {
    const moduleNames = {
      'budget': '预算',
      'rough-estimate': '概算',
      'settlement': '结算',
      'audit': '审查',
      'material': '工料机'
    }

    const moduleName = moduleNames[moduleType] || moduleType
    message.info(`刷新${moduleName}项目树`)
  }

  /**
   * 展开所有项目节点
   * @param {Array} keys - 所有节点的键值数组
   * @param {Object} config - 项目树配置对象
   */
  const handleProjectExpandAll = (keys, config) => {
    config.expandedProjectKeys.value = keys
    message.info('展开所有项目')
  }

  /**
   * 收起所有项目节点
   * @param {Object} config - 项目树配置对象
   */
  const handleProjectCollapseAll = (config) => {
    config.expandedProjectKeys.value = []
    message.info('收起所有项目')
  }

  return {
    getStandardProjectTree,
    getDefaultProjectTreeConfig,
    initializeProjectTree,
    handleProjectSelect,
    handleProjectExpand,
    handleProjectRefresh,
    handleProjectExpandAll,
    handleProjectCollapseAll
  }
}