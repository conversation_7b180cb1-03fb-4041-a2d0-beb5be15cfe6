<template>
  <div class="budget-item-detail">
    <!-- 使用配置化的ItemDetail组件 -->
    <ConfigurableItemDetail
      module-type="yuSuan"
      :table-data="tableData"
      :custom-field-config="budgetFieldConfig"
      :edit-conditions="budgetEditConditions"
      :project-data="projectData"
      :tab-info="tabInfo"
      :level-type="levelType"
      @data-change="handleDataChange"
      @action="handleAction"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { ConfigurableItemDetail } from '@cost-app/shared-components'

const props = defineProps({
  moduleType: String,
  levelType: Number,
  tabInfo: Object,
  projectData: Object,
  tableData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 预算模块特有的字段配置
const budgetFieldConfig = ref({
  // 自定义字段配置，覆盖默认配置
  budgetUnitPrice: {
    title: '预算单价',
    component: 'number',
    componentProps: {
      precision: 2,
      min: 0,
      placeholder: '请输入预算单价'
    },
    required: true
  },
  quotaCode: {
    title: '定额编号',
    component: 'select',
    componentProps: {
      showSearch: true,
      placeholder: '请选择定额编号'
    }
  },
  laborCost: {
    editable: false, // 人工费不可直接编辑
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  },
  materialCost: {
    editable: false, // 材料费不可直接编辑
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  },
  mechanicalCost: {
    editable: false, // 机械费不可直接编辑
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  }
})

// 预算模块的编辑条件
const budgetEditConditions = ref({
  // 工程量编辑条件：预算阶段都可以编辑
  quantity: () => true,

  // 预算单价编辑条件：根据项目状态决定
  budgetUnitPrice: (context) => {
    const { record } = context
    return record.status !== 'locked' && record.status !== 'approved'
  },

  // 定额相关字段编辑条件
  quotaCode: (context) => {
    const { record } = context
    return record.type !== 'measure' // 措施项目不需要定额
  }
})

// 事件处理
const handleDataChange = (newData) => {
  emit('dataChange', newData)
}

const handleAction = (actionType, actionData) => {
  console.log('预算模块操作:', actionType, actionData)

  switch (actionType) {
    case 'cellEdited':
      message.success('预算数据更新成功')
      break
    case 'addRow':
      message.success('新增预算项目成功')
      break
    case 'deleteRow':
      message.success('删除预算项目成功')
      break
    case 'contextMenu':
      handleContextMenu(actionData)
      break
    default:
      // 其他操作直接传递给父组件
      emit('action', actionType, actionData)
  }
}

const handleContextMenu = ({ key, record }) => {
  switch (key) {
    case 'copy':
      message.success(`复制项目: ${record.name}`)
      break
    case 'paste':
      message.success('粘贴项目')
      break
    case 'delete':
      message.success(`删除项目: ${record.name}`)
      break
    case 'addSub':
      message.success(`为 ${record.name} 添加子项目`)
      break
    case 'viewDetail':
      message.info(`查看项目详情: ${record.name}`)
      break
    case 'exportItem':
      message.success(`导出项目: ${record.name}`)
      break
    default:
      console.log('未处理的右键菜单操作:', key, record)
  }
}
</script>

<style lang="scss" scoped>
.budget-item-detail {
  height: 100%;
  overflow: hidden;
}
</style>
    unit: 'm³',
    quantity: 1500,
    budgetUnitPrice: 25.50,
    budgetAmount: 38250,
    quotaCode: 'A1-1',
    quotaUnitPrice: 25.50,
    laborCost: 12000,
    materialCost: 18000,
    mechanicalCost: 8250,
    remark: '基础开挖',
    type: 'direct',
    children: [
      {
        id: 11,
        code: 'B01-001-01',
        name: '人工挖土',
        specification: '深度2m以内',
        unit: 'm³',
        quantity: 500,
        budgetUnitPrice: 35.00,
        budgetAmount: 17500,
        quotaCode: 'A1-1-1',
        quotaUnitPrice: 35.00,
        laborCost: 15000,
        materialCost: 2000,
        mechanicalCost: 500,
        remark: '人工开挖部分',
        type: 'direct'
      },
      {
        id: 12,
        code: 'B01-001-02',
        name: '机械挖土',
        specification: '挖掘机开挖',
        unit: 'm³',
        quantity: 1000,
        budgetUnitPrice: 20.75,
        budgetAmount: 20750,
        quotaCode: 'A1-1-2',
        quotaUnitPrice: 20.75,
        laborCost: 3000,
        materialCost: 2000,
        mechanicalCost: 15750,
        remark: '机械开挖部分',
        type: 'direct'
      }
    ]
  },
  {
    id: 2,
    code: 'B02-001',
    name: '混凝土工程',
    specification: 'C30商品混凝土',
    unit: 'm³',
    quantity: 800,
    budgetUnitPrice: 450.00,
    budgetAmount: 360000,
    quotaCode: 'A2-1',
    quotaUnitPrice: 450.00,
    laborCost: 80000,
    materialCost: 240000,
    mechanicalCost: 40000,
    remark: '基础混凝土',
    type: 'direct',
    children: [
      {
        id: 21,
        code: 'B02-001-01',
        name: '混凝土浇筑',
        specification: '泵送混凝土',
        unit: 'm³',
        quantity: 800,
        budgetUnitPrice: 380.00,
        budgetAmount: 304000,
        quotaCode: 'A2-1-1',
        quotaUnitPrice: 380.00,
        laborCost: 60000,
        materialCost: 220000,
        mechanicalCost: 24000,
        remark: '混凝土浇筑',
        type: 'direct'
      },
      {
        id: 22,
        code: 'B02-001-02',
        name: '混凝土养护',
        specification: '标准养护',
        unit: 'm³',
        quantity: 800,
        budgetUnitPrice: 70.00,
        budgetAmount: 56000,
        quotaCode: 'A2-1-2',
        quotaUnitPrice: 70.00,
        laborCost: 20000,
        materialCost: 20000,
        mechanicalCost: 16000,
        remark: '养护费用',
        type: 'direct'
      }
    ]
  },
  {
    id: 3,
    code: 'B03-001',
    name: '钢筋工程',
    specification: 'HRB400钢筋',
    unit: 't',
    quantity: 120,
    budgetUnitPrice: 4800.00,
    budgetAmount: 576000,
    quotaCode: 'A3-1',
    quotaUnitPrice: 4800.00,
    laborCost: 180000,
    materialCost: 360000,
    mechanicalCost: 36000,
    remark: '结构钢筋',
    type: 'direct',
    children: [
      {
        id: 31,
        code: 'B03-001-01',
        name: '钢筋制作',
        specification: 'φ12-φ25钢筋',
        unit: 't',
        quantity: 80,
        budgetUnitPrice: 4200.00,
        budgetAmount: 336000,
        quotaCode: 'A3-1-1',
        quotaUnitPrice: 4200.00,
        laborCost: 120000,
        materialCost: 180000,
        mechanicalCost: 36000,
        remark: '钢筋下料、成型',
        type: 'direct'
      },
      {
        id: 32,
        code: 'B03-001-02',
        name: '钢筋安装',
        specification: '现场绑扎',
        unit: 't',
        quantity: 120,
        budgetUnitPrice: 2000.00,
        budgetAmount: 240000,
        quotaCode: 'A3-1-2',
        quotaUnitPrice: 2000.00,
        laborCost: 180000,
        materialCost: 60000,
        mechanicalCost: 0,
        remark: '钢筋绑扎安装',
        type: 'direct'
      }
    ]
  },
  {
    id: 4,
    code: 'B04-001',
    name: '砌体工程',
    specification: 'MU10烧结砖',
    unit: 'm³',
    quantity: 600,
    budgetUnitPrice: 380.00,
    budgetAmount: 228000,
    quotaCode: 'A4-1',
    quotaUnitPrice: 380.00,
    laborCost: 120000,
    materialCost: 90000,
    mechanicalCost: 18000,
    remark: '外墙砌体',
    type: 'direct',
    children: [
      {
        id: 41,
        code: 'B04-001-01',
        name: '砖砌体',
        specification: 'M7.5水泥砂浆',
        unit: 'm³',
        quantity: 500,
        budgetUnitPrice: 360.00,
        budgetAmount: 180000,
        quotaCode: 'A4-1-1',
        quotaUnitPrice: 360.00,
        laborCost: 100000,
        materialCost: 70000,
        mechanicalCost: 10000,
        remark: '标准砖砌体',
        type: 'direct'
      },
      {
        id: 42,
        code: 'B04-001-02',
        name: '构造柱',
        specification: 'C20混凝土',
        unit: 'm³',
        quantity: 100,
        budgetUnitPrice: 480.00,
        budgetAmount: 48000,
        quotaCode: 'A4-1-2',
        quotaUnitPrice: 480.00,
        laborCost: 20000,
        materialCost: 20000,
        mechanicalCost: 8000,
        remark: '构造柱浇筑',
        type: 'direct'
      }
    ]
  },
  {
    id: 5,
    code: 'B05-001',
    name: '屋面工程',
    specification: 'SBS防水卷材',
    unit: 'm²',
    quantity: 1200,
    budgetUnitPrice: 85.00,
    budgetAmount: 102000,
    quotaCode: 'A5-1',
    quotaUnitPrice: 85.00,
    laborCost: 36000,
    materialCost: 54000,
    mechanicalCost: 12000,
    remark: '屋面防水',
    type: 'direct',
    children: [
      {
        id: 51,
        code: 'B05-001-01',
        name: '找平层',
        specification: '20厚1:3水泥砂浆',
        unit: 'm²',
        quantity: 1200,
        budgetUnitPrice: 25.00,
        budgetAmount: 30000,
        quotaCode: 'A5-1-1',
        quotaUnitPrice: 25.00,
        laborCost: 12000,
        materialCost: 15000,
        mechanicalCost: 3000,
        remark: '屋面找平',
        type: 'direct'
      },
      {
        id: 52,
        code: 'B05-001-02',
        name: '防水层',
        specification: '3mm厚SBS卷材',
        unit: 'm²',
        quantity: 1200,
        budgetUnitPrice: 60.00,
        budgetAmount: 72000,
        quotaCode: 'A5-1-2',
        quotaUnitPrice: 60.00,
        laborCost: 24000,
        materialCost: 39000,
        mechanicalCost: 9000,
        remark: '防水卷材铺贴',
        type: 'direct'
      }
    ]
  },
  {
    id: 6,
    code: 'B06-001',
    name: '门窗工程',
    specification: '塑钢门窗',
    unit: 'm²',
    quantity: 320,
    budgetUnitPrice: 450.00,
    budgetAmount: 144000,
    quotaCode: 'A6-1',
    quotaUnitPrice: 450.00,
    laborCost: 32000,
    materialCost: 96000,
    mechanicalCost: 16000,
    remark: '塑钢门窗安装',
    type: 'direct'
  },
  {
    id: 7,
    code: 'B07-001',
    name: '装饰工程',
    specification: '内外墙装饰',
    unit: 'm²',
    quantity: 2800,
    budgetUnitPrice: 120.00,
    budgetAmount: 336000,
    quotaCode: 'A7-1',
    quotaUnitPrice: 120.00,
    laborCost: 168000,
    materialCost: 140000,
    mechanicalCost: 28000,
    remark: '墙面装饰',
    type: 'direct',
    children: [
      {
        id: 71,
        code: 'B07-001-01',
        name: '内墙抹灰',
        specification: '20厚1:3水泥砂浆',
        unit: 'm²',
        quantity: 1500,
        budgetUnitPrice: 35.00,
        budgetAmount: 52500,
        quotaCode: 'A7-1-1',
        quotaUnitPrice: 35.00,
        laborCost: 30000,
        materialCost: 18000,
        mechanicalCost: 4500,
        remark: '内墙抹灰',
        type: 'direct'
      },
      {
        id: 72,
        code: 'B07-001-02',
        name: '外墙涂料',
        specification: '弹性涂料',
        unit: 'm²',
        quantity: 1300,
        budgetUnitPrice: 45.00,
        budgetAmount: 58500,
        quotaCode: 'A7-1-2',
        quotaUnitPrice: 45.00,
        laborCost: 26000,
        materialCost: 26000,
        mechanicalCost: 6500,
        remark: '外墙涂料',
        type: 'direct'
      }
    ]
  },
  {
    id: 8,
    code: 'B08-001',
    name: '给排水工程',
    specification: 'PPR给水管',
    unit: 'm',
    quantity: 580,
    budgetUnitPrice: 45.00,
    budgetAmount: 26100,
    quotaCode: 'A8-1',
    quotaUnitPrice: 45.00,
    laborCost: 11600,
    materialCost: 11600,
    mechanicalCost: 2900,
    remark: '室内给排水',
    type: 'direct'
  },
  {
    id: 90,
    code: 'C01-001',
    name: '脚手架工程',
    specification: '钢管脚手架',
    unit: 'm²',
    quantity: 2000,
    budgetUnitPrice: 45.00,
    budgetAmount: 90000,
    quotaCode: 'C1-1',
    quotaUnitPrice: 45.00,
    laborCost: 50000,
    materialCost: 30000,
    mechanicalCost: 10000,
    remark: '外墙脚手架',
    type: 'measure'
  },
  {
    id: 100,
    code: 'C02-001',
    name: '模板工程',
    specification: '木模板',
    unit: 'm²',
    quantity: 1500,
    budgetUnitPrice: 85.00,
    budgetAmount: 127500,
    quotaCode: 'C2-1',
    quotaUnitPrice: 85.00,
    laborCost: 75000,
    materialCost: 45000,
    mechanicalCost: 7500,
    remark: '结构模板',
    type: 'measure'
  },
  {
    id: 110,
    code: 'C03-001',
    name: '垂直运输',
    specification: '塔式起重机',
    unit: 't',
    quantity: 2500,
    budgetUnitPrice: 25.00,
    budgetAmount: 62500,
    quotaCode: 'C3-1',
    quotaUnitPrice: 25.00,
    laborCost: 18750,
    materialCost: 25000,
    mechanicalCost: 18750,
    remark: '材料垂直运输',
    type: 'measure'
  },
  {
    id: 120,
    code: 'C04-001',
    name: '安全文明施工',
    specification: '标准化工地',
    unit: 'm²',
    quantity: 5000,
    budgetUnitPrice: 15.00,
    budgetAmount: 75000,
    quotaCode: 'C4-1',
    quotaUnitPrice: 15.00,
    laborCost: 30000,
    materialCost: 30000,
    mechanicalCost: 15000,
    remark: '安全防护费用',
    type: 'measure'
  }
]

// 监听数据变化
watch(() => props.tableData, () => {
  initializeData()
}, { deep: true })

onMounted(() => {
  initializeData()
})

// 其他事件处理方法...
const handleRowSelect = (selectedKeys, selectedRows) => {
  emit('action', 'rowSelect', { selectedKeys, selectedRows })
}

const handleAddRow = (newRow) => {
  emit('action', 'addRow', { ...newRow, type: 'budget' })
}

const handleDeleteRow = (deletedRows) => {
  emit('action', 'deleteRow', deletedRows)
}

// 右键菜单处理
const handleContextMenuClick = ({ key }, record) => {
  console.log('右键菜单操作:', { key, record })

  switch (key) {
    case 'copy':
      message.success(`复制项目: ${record.name}`)
      emit('action', 'copy', record)
      break
    case 'paste':
      message.success('粘贴项目')
      emit('action', 'paste', record)
      break
    case 'delete':
      message.success(`删除项目: ${record.name}`)
      emit('action', 'delete', record)
      break
    case 'addSub':
      message.success(`为 ${record.name} 添加子项目`)
      emit('action', 'addSub', record)
      break
    case 'moveUp':
      message.success(`上移项目: ${record.name}`)
      emit('action', 'moveUp', record)
      break
    case 'moveDown':
      message.success(`下移项目: ${record.name}`)
      emit('action', 'moveDown', record)
      break
    case 'expand':
      message.info(`展开项目: ${record.name}`)
      emit('action', 'expand', record)
      break
    case 'collapse':
      message.info(`收起项目: ${record.name}`)
      emit('action', 'collapse', record)
      break
    case 'viewDetail':
      message.info(`查看项目详情: ${record.name}`)
      emit('action', 'viewDetail', record)
      break
    case 'exportItem':
      message.success(`导出项目: ${record.name}`)
      emit('action', 'exportItem', record)
      break
    default:
      emit('action', 'contextMenu', { key, record })
  }
}

</script>

<style lang="scss" scoped>
.budget-item-detail {
  height: 100%;
  overflow: hidden;
}
</style>