<template>
  <div class="budget-item-detail">
    <!-- 使用配置化的ItemDetail组件 -->
    <ConfigurableItemDetail
      module-type="yuSuan"
      :table-data="computedTableData"
      :custom-field-config="budgetFieldConfig"
      :edit-conditions="budgetEditConditions"
      :project-data="projectData"
      :tab-info="tabInfo"
      :level-type="levelType"
      @data-change="handleDataChange"
      @action="handleAction"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { ConfigurableItemDetail } from '@cost-app/shared-components'

const props = defineProps({
  moduleType: String,
  levelType: Number,
  tabInfo: Object,
  projectData: Object,
  tableData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 如果没有传入数据，提供一些测试数据
const computedTableData = computed(() => {
  console.log('BudgetItemDetail - props.tableData:', props.tableData)

  if (props.tableData && props.tableData.length > 0) {
    console.log('BudgetItemDetail - 使用传入的数据')
    return props.tableData
  }

  console.log('BudgetItemDetail - 使用测试数据')
  // 返回预算模块的测试数据
  return [
    {
      id: 1,
      bdCode: 'B01-001',
      name: '土石方工程',
      projectAttr: '一般土壤开挖',
      specification: '机械开挖',
      unit: 'm³',
      quantity: 1500,
      budgetUnitPrice: 25.50,
      budgetAmount: 38250.00,
      quotaCode: 'A01-001',
      quotaUnitPrice: 24.80,
      laborCost: 8.50,
      materialCost: 12.30,
      mechanicalCost: 4.70,
      type: 'main',
      status: 'normal'
    },
    {
      id: 2,
      bdCode: 'B01-002',
      name: '混凝土工程',
      projectAttr: 'C30混凝土浇筑',
      specification: '现浇',
      unit: 'm³',
      quantity: 800,
      budgetUnitPrice: 450.00,
      budgetAmount: 360000.00,
      quotaCode: 'A02-001',
      quotaUnitPrice: 435.00,
      laborCost: 120.00,
      materialCost: 280.00,
      mechanicalCost: 50.00,
      type: 'main',
      status: 'normal'
    },
    {
      id: 3,
      bdCode: 'B01-003',
      name: '钢筋工程',
      projectAttr: 'HRB400钢筋',
      specification: 'Φ12-25',
      unit: 't',
      quantity: 120,
      budgetUnitPrice: 4800.00,
      budgetAmount: 576000.00,
      quotaCode: 'A03-001',
      quotaUnitPrice: 4750.00,
      laborCost: 800.00,
      materialCost: 3800.00,
      mechanicalCost: 200.00,
      type: 'main',
      status: 'normal'
    }
  ]
})

// 预算模块特有的字段配置
const budgetFieldConfig = ref({
  // 自定义字段配置，覆盖默认配置
  budgetUnitPrice: {
    title: '预算单价',
    component: 'number',
    componentProps: {
      precision: 2,
      min: 0,
      placeholder: '请输入预算单价'
    },
    required: true
  },
  quotaCode: {
    title: '定额编号',
    component: 'select',
    componentProps: {
      showSearch: true,
      placeholder: '请选择定额编号'
    }
  },
  laborCost: {
    editable: false, // 人工费不可直接编辑
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  },
  materialCost: {
    editable: false, // 材料费不可直接编辑
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  },
  mechanicalCost: {
    editable: false, // 机械费不可直接编辑
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  }
})

// 预算模块的编辑条件
const budgetEditConditions = ref({
  // 工程量编辑条件：预算阶段都可以编辑
  quantity: () => true,

  // 预算单价编辑条件：根据项目状态决定
  budgetUnitPrice: (context) => {
    const { record } = context
    return record.status !== 'locked' && record.status !== 'approved'
  },

  // 定额相关字段编辑条件
  quotaCode: (context) => {
    const { record } = context
    return record.type !== 'measure' // 措施项目不需要定额
  }
})

// 事件处理
const handleDataChange = (newData) => {
  emit('dataChange', newData)
}

const handleAction = (actionType, actionData) => {
  console.log('预算模块操作:', actionType, actionData)

  switch (actionType) {
    case 'cellEdited':
      message.success('预算数据更新成功')
      break
    case 'addRow':
      message.success('新增预算项目成功')
      break
    case 'deleteRow':
      message.success('删除预算项目成功')
      break
    case 'contextMenu':
      handleContextMenu(actionData)
      break
    default:
      // 其他操作直接传递给父组件
      emit('action', actionType, actionData)
  }
}

const handleContextMenu = ({ key, record }) => {
  switch (key) {
    case 'copy':
      message.success(`复制项目: ${record.name}`)
      break
    case 'paste':
      message.success('粘贴项目')
      break
    case 'delete':
      message.success(`删除项目: ${record.name}`)
      break
    case 'addSub':
      message.success(`为 ${record.name} 添加子项目`)
      break
    case 'viewDetail':
      message.info(`查看项目详情: ${record.name}`)
      break
    case 'exportItem':
      message.success(`导出项目: ${record.name}`)
      break
    default:
      console.log('未处理的右键菜单操作:', key, record)
  }
}
</script>

<style lang="scss" scoped>
.budget-item-detail {
  height: 100%;
  overflow: hidden;
}
</style>