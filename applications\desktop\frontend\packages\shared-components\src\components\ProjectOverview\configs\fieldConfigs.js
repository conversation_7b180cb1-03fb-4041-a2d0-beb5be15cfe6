/**
 * 字段配置文件
 * 定义各种字段的配置信息和验证规则
 */

// 基础字段类型配置
export const fieldTypeConfigs = {
  text: {
    component: 'a-input',
    defaultProps: {
      placeholder: '请输入',
      maxLength: 100
    },
    validator: (value, config) => {
      if (config.required && (!value || value.trim() === '')) {
        return { valid: false, message: '此字段为必填项' }
      }
      if (value && config.maxLength && value.length > config.maxLength) {
        return { valid: false, message: `长度不能超过${config.maxLength}个字符` }
      }
      if (value && config.pattern && !config.pattern.test(value)) {
        return { valid: false, message: '格式不正确' }
      }
      return { valid: true }
    }
  },
  
  number: {
    component: 'a-input-number',
    defaultProps: {
      placeholder: '请输入数字',
      precision: 2,
      min: 0,
      style: { width: '100%' }
    },
    validator: (value, config) => {
      if (config.required && (value === null || value === undefined || value === '')) {
        return { valid: false, message: '此字段为必填项' }
      }
      if (value !== null && value !== undefined) {
        if (config.min !== undefined && value < config.min) {
          return { valid: false, message: `不能小于${config.min}` }
        }
        if (config.max !== undefined && value > config.max) {
          return { valid: false, message: `不能大于${config.max}` }
        }
      }
      return { valid: true }
    },
    formatter: (value, config) => {
      if (value === null || value === undefined || value === '') return ''
      const num = Number(value)
      if (isNaN(num)) return value
      
      const precision = config.precision || 2
      const formatted = num.toFixed(precision)
      
      if (config.suffix) {
        return `${formatted} ${config.suffix}`
      }
      return formatted
    }
  },
  
  date: {
    component: 'a-date-picker',
    defaultProps: {
      placeholder: '请选择日期',
      format: 'YYYY-MM-DD',
      style: { width: '100%' }
    },
    validator: (value, config) => {
      if (config.required && !value) {
        return { valid: false, message: '此字段为必填项' }
      }
      return { valid: true }
    },
    formatter: (value, config) => {
      if (!value) return ''
      
      try {
        const date = new Date(value)
        if (isNaN(date.getTime())) return value
        
        const format = config.format || 'YYYY-MM-DD'
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        
        return format
          .replace('YYYY', year)
          .replace('MM', month)
          .replace('DD', day)
      } catch (error) {
        return value
      }
    }
  },
  
  select: {
    component: 'a-select',
    defaultProps: {
      placeholder: '请选择',
      showSearch: true,
      style: { width: '100%' }
    },
    validator: (value, config) => {
      if (config.required && (!value || value === '')) {
        return { valid: false, message: '此字段为必填项' }
      }
      if (value && config.options) {
        const validValues = config.options.map(opt => opt.value)
        if (!validValues.includes(value)) {
          return { valid: false, message: '选择的值无效' }
        }
      }
      return { valid: true }
    },
    formatter: (value, config) => {
      if (!value || !config.options) return value
      
      const option = config.options.find(opt => opt.value === value)
      return option ? option.label : value
    }
  },
  
  textarea: {
    component: 'a-textarea',
    defaultProps: {
      placeholder: '请输入',
      rows: 3,
      maxLength: 500,
      showCount: true
    },
    validator: (value, config) => {
      if (config.required && (!value || value.trim() === '')) {
        return { valid: false, message: '此字段为必填项' }
      }
      if (value && config.maxLength && value.length > config.maxLength) {
        return { valid: false, message: `长度不能超过${config.maxLength}个字符` }
      }
      return { valid: true }
    }
  },
  
  checkbox: {
    component: 'a-checkbox',
    defaultProps: {
      checkedChildren: '是',
      unCheckedChildren: '否'
    },
    validator: (value, config) => {
      if (config.required && !value) {
        return { valid: false, message: '此字段为必填项' }
      }
      return { valid: true }
    },
    formatter: (value, config) => {
      return value ? '是' : '否'
    }
  }
}

// 预定义字段配置
export const predefinedFields = {
  // 基本信息字段
  projectName: {
    name: '工程名称',
    type: 'text',
    required: true,
    maxLength: 100,
    highlight: false,
    placeholder: '请输入工程名称'
  },
  
  projectCode: {
    name: '项目编号',
    type: 'text',
    required: true,
    maxLength: 50,
    highlight: false,
    placeholder: '请输入项目编号',
    pattern: /^[A-Z0-9-]+$/
  },
  
  buildingArea: {
    name: '建筑面积',
    type: 'number',
    required: true,
    precision: 2,
    min: 0,
    suffix: '㎡',
    highlight: true,
    placeholder: '请输入建筑面积'
  },
  
  projectScale: {
    name: '工程规模',
    type: 'number',
    required: false,
    precision: 2,
    min: 0,
    suffix: '万元',
    highlight: true,
    placeholder: '请输入工程规模'
  },
  
  constructionUnit: {
    name: '建设单位',
    type: 'text',
    required: false,
    maxLength: 100,
    highlight: false,
    placeholder: '请输入建设单位'
  },
  
  designUnit: {
    name: '设计单位',
    type: 'text',
    required: false,
    maxLength: 100,
    highlight: false,
    placeholder: '请输入设计单位'
  },
  
  compileDate: {
    name: '编制时间',
    type: 'date',
    required: true,
    format: 'YYYY-MM-DD',
    highlight: true,
    placeholder: '请选择编制时间'
  },
  
  compiler: {
    name: '编制人',
    type: 'text',
    required: true,
    maxLength: 50,
    highlight: true,
    placeholder: '请输入编制人'
  },
  
  reviewer: {
    name: '核对人(复核人)',
    type: 'text',
    required: false,
    maxLength: 50,
    highlight: true,
    placeholder: '请输入核对人'
  },
  
  reviewDate: {
    name: '核对(复核)时间',
    type: 'date',
    required: false,
    format: 'YYYY-MM-DD',
    highlight: true,
    placeholder: '请选择核对时间'
  },
  
  // 工程特征字段
  structureType: {
    name: '结构类型',
    type: 'select',
    required: false,
    options: [
      { label: '框架结构', value: 'frame' },
      { label: '剪力墙结构', value: 'shear_wall' },
      { label: '框剪结构', value: 'frame_shear' },
      { label: '砖混结构', value: 'brick_concrete' },
      { label: '钢结构', value: 'steel' },
      { label: '其他', value: 'other' }
    ],
    placeholder: '请选择结构类型'
  },
  
  decorationStandard: {
    name: '装修标准',
    type: 'select',
    required: false,
    options: [
      { label: '毛坯', value: 'rough' },
      { label: '简装', value: 'simple' },
      { label: '精装', value: 'fine' },
      { label: '豪装', value: 'luxury' }
    ],
    placeholder: '请选择装修标准'
  },
  
  foundationType: {
    name: '基础类型',
    type: 'select',
    required: false,
    options: [
      { label: '独立基础', value: 'isolated' },
      { label: '条形基础', value: 'strip' },
      { label: '筏板基础', value: 'raft' },
      { label: '桩基础', value: 'pile' },
      { label: '其他', value: 'other' }
    ],
    placeholder: '请选择基础类型'
  }
}

// 高亮显示的字段列表
export const highlightFields = [
  '建筑面积',
  '工程规模',
  '编制单位法定代表人',
  '招标人(发包人)',
  '招标人(发包人)法人或其授权人',
  '工程造价咨询人',
  '工程造价咨询人法人或其授权人',
  '编制人',
  '编制时间',
  '核对人(复核人)',
  '核对(复核)时间',
  '投标人(承包人)',
  '投标人(承包人)法人或其授权人'
]

// 日期字段列表
export const dateFields = [
  '开工日期',
  '竣工日期',
  '编制时间',
  '核对(复核)时间'
]

// 数值字段列表
export const numberFields = [
  '建筑面积',
  '工程规模'
]

/**
 * 获取字段配置
 * @param {String} fieldName 字段名称
 * @param {Object} moduleConfig 模块配置
 * @returns {Object} 字段配置
 */
export function getFieldConfig(fieldName, moduleConfig = {}) {
  // 优先使用模块特定配置
  if (moduleConfig[fieldName]) {
    return {
      ...predefinedFields[fieldName],
      ...moduleConfig[fieldName]
    }
  }
  
  // 使用预定义配置
  if (predefinedFields[fieldName]) {
    return predefinedFields[fieldName]
  }
  
  // 根据字段名称推断配置
  const inferredConfig = inferFieldConfig(fieldName)
  
  return {
    name: fieldName,
    type: 'text',
    required: false,
    editable: true,
    ...inferredConfig
  }
}

/**
 * 根据字段名称推断配置
 * @param {String} fieldName 字段名称
 * @returns {Object} 推断的配置
 */
function inferFieldConfig(fieldName) {
  const config = {}
  
  // 判断是否为高亮字段
  if (highlightFields.includes(fieldName)) {
    config.highlight = true
  }
  
  // 判断字段类型
  if (dateFields.includes(fieldName) || fieldName.includes('时间') || fieldName.includes('日期')) {
    config.type = 'date'
    config.format = 'YYYY-MM-DD'
  } else if (numberFields.includes(fieldName) || fieldName.includes('面积') || fieldName.includes('金额') || fieldName.includes('规模')) {
    config.type = 'number'
    config.precision = 2
    config.min = 0
    
    if (fieldName.includes('面积')) {
      config.suffix = '㎡'
    } else if (fieldName.includes('金额') || fieldName.includes('规模')) {
      config.suffix = '万元'
    }
  } else if (fieldName.includes('类型') || fieldName.includes('标准') || fieldName.includes('等级')) {
    config.type = 'select'
    config.options = [] // 需要动态加载选项
  } else if (fieldName.includes('说明') || fieldName.includes('备注') || fieldName.includes('描述')) {
    config.type = 'textarea'
    config.rows = 3
    config.maxLength = 500
  }
  
  return config
}

/**
 * 获取字段类型配置
 * @param {String} type 字段类型
 * @returns {Object} 类型配置
 */
export function getFieldTypeConfig(type) {
  return fieldTypeConfigs[type] || fieldTypeConfigs.text
}

/**
 * 验证字段值
 * @param {Any} value 字段值
 * @param {Object} config 字段配置
 * @returns {Object} 验证结果
 */
export function validateField(value, config) {
  if (!config) {
    return { valid: true }
  }
  
  const typeConfig = getFieldTypeConfig(config.type)
  
  if (typeConfig.validator) {
    return typeConfig.validator(value, config)
  }
  
  return { valid: true }
}

/**
 * 格式化字段值
 * @param {Any} value 字段值
 * @param {Object} config 字段配置
 * @returns {String} 格式化后的值
 */
export function formatFieldValue(value, config) {
  if (!config) {
    return value
  }
  
  const typeConfig = getFieldTypeConfig(config.type)
  
  if (typeConfig.formatter) {
    return typeConfig.formatter(value, config)
  }
  
  return value
}

/**
 * 获取字段组件属性
 * @param {Object} config 字段配置
 * @returns {Object} 组件属性
 */
export function getFieldComponentProps(config) {
  if (!config) {
    return {}
  }
  
  const typeConfig = getFieldTypeConfig(config.type)
  
  return {
    ...typeConfig.defaultProps,
    ...config.componentProps,
    placeholder: config.placeholder || typeConfig.defaultProps?.placeholder
  }
}

/**
 * 检查字段是否为必填
 * @param {String} fieldName 字段名称
 * @param {Object} moduleConfig 模块配置
 * @returns {Boolean} 是否必填
 */
export function isFieldRequired(fieldName, moduleConfig = {}) {
  const config = getFieldConfig(fieldName, moduleConfig)
  return config.required || false
}

/**
 * 检查字段是否需要高亮
 * @param {String} fieldName 字段名称
 * @param {Object} moduleConfig 模块配置
 * @returns {Boolean} 是否高亮
 */
export function isFieldHighlight(fieldName, moduleConfig = {}) {
  const config = getFieldConfig(fieldName, moduleConfig)
  return config.highlight || false
}
