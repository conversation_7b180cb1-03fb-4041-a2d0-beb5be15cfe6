# CostTable 模块化组件使用指南

## 概述

CostTable 是一个统一的造价表格组件，支持概算、预算、结算、审核、工料机五个模块的业务需求。通过配置驱动的方式，实现了代码复用和维护性提升。

## 核心特性

- 🎯 **模块化架构**: 支持五个造价模块的差异化需求
- 🔧 **配置驱动**: 通过配置控制列结构、菜单项、编辑条件等
- 🌳 **树形结构**: 支持分部分项的树形展示和操作
- 📝 **行内编辑**: 支持单元格级别的编辑控制
- 🎨 **右键菜单**: 根据模块类型动态生成菜单
- 🔌 **插件化**: 模块特有功能通过插件方式扩展

## 快速开始

### 基本使用

```vue
<template>
  <CostTable
    :data="tableData"
    module-type="yuSuan"
    :editable="true"
    @data-change="handleDataChange"
    @component-action="handleComponentAction"
  />
</template>

<script setup>
import { ref } from 'vue'
import { CostTable } from '@cost-app/shared-components'

const tableData = ref([
  {
    id: 1,
    kind: '01',
    bdCode: '01',
    name: '土建工程',
    level: 0,
    children: [
      {
        id: 2,
        kind: '03',
        bdCode: '010101001',
        name: '挖土方',
        unit: 'm³',
        quantity: 100,
        price: 25.5,
        level: 1
      }
    ]
  }
])

const handleDataChange = (data) => {
  console.log('数据变更:', data)
}

const handleComponentAction = (actionData) => {
  console.log('组件操作:', actionData)
}
</script>
```

### 模块类型配置

支持的模块类型：

- `gaiSuan`: 概算模块
- `yuSuan`: 预算模块  
- `jieSuan`: 结算模块
- `shenHe`: 审核模块
- `gongLiaoJi`: 工料机模块

## Props 配置

### 基础配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| data | Array | [] | 表格数据 |
| moduleType | String | 'yuSuan' | 模块类型 |
| editable | Boolean | true | 是否可编辑 |
| rangeSelection | Boolean | true | 是否支持范围选择 |
| bordered | Boolean | true | 是否显示边框 |

### 高级配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| columns | Array | [] | 自定义列配置 |
| moduleConfig | Object | {} | 模块配置覆盖 |
| treeCodeField | String | 'code' | 树形结构编码字段 |
| customFormComponent | Component | null | 自定义表单组件 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| dataChange | data | 数据变更 |
| rowSelect | selectedRows | 行选择变更 |
| cellEdit | editData | 单元格编辑开始 |
| cellEdited | editData | 单元格编辑完成 |
| addRow | addData | 新增行 |
| deleteRow | record | 删除行 |
| copyRows | rows | 复制行 |
| pasteRows | pasteData | 粘贴行 |
| componentAction | actionData | 模块组件操作 |
| recordUpdate | updateData | 记录更新 |

## 模块差异配置

### 字段映射

```javascript
// 概算/工料机模块
{
  codeField: 'deCode',    // 定额编码
  nameField: 'deName'     // 定额名称
}

// 预算/结算/审核模块  
{
  codeField: 'bdCode',    // 清单编码
  nameField: 'name'       // 项目名称
}
```

### 功能特性

```javascript
// 概算模块特性
{
  hasCheckbox: false,                    // 无复选框
  hasProjectAttr: false,                 // 无项目特征
  supportsIntroductionQuantity: true,    // 支持引入工程量
  supportsBatchModifyMainMaterials: true // 支持批量修改主材
}

// 预算模块特性
{
  hasCheckbox: true,           // 有复选框
  hasProjectAttr: true,        // 有项目特征
  supportsCodeReset: true,     // 支持编码重置
  supportsSyncNameToDE: true   // 支持同步名称到定额
}
```

## 右键菜单

### 通用菜单项

- 插入 (添加分部/子分部/清单/子目)
- 复制/粘贴
- 删除 (预算/结算/审核)
- 锁定/解锁

### 模块特有菜单

#### 概算模块
- 复制单元格内容
- 粘贴为子项
- 引入工程量
- 批量修改主材
- 整理分部

#### 审核模块
- 审核对比
- 审核意见
- 临时删除

#### 结算模块
- 关联合同
- 进度结算

#### 工料机模块
- 标准换算
- 设置主材
- 项目属性关联

## 扩展开发

### 添加新的模块特有组件

1. 在 `CostTable/modules/` 目录下创建模块文件夹
2. 实现组件并导出
3. 在 `CostTableModules.vue` 中注册组件
4. 在 `moduleConfigs.js` 中添加配置

```javascript
// 1. 创建组件 modules/custom/CustomComponent.vue
<template>
  <a-modal v-model:open="visible" title="自定义功能">
    <!-- 组件内容 -->
  </a-modal>
</template>

// 2. 在 CostTableModules.vue 中注册
const CustomComponent = defineAsyncComponent(() => 
  import('../modules/custom/CustomComponent.vue')
)

// 3. 在模板中使用
<component
  v-if="activeComponent === 'customAction'"
  :is="CustomComponent"
  v-model:visible="componentVisible"
  @confirm="handleCustomAction"
/>

// 4. 在 moduleConfigs.js 中添加配置
const MODULE_CONFIGS = {
  customModule: {
    name: '自定义模块',
    features: {
      supportsCustomAction: true
    }
  }
}
```

### 自定义列配置

```javascript
import { generateColumnConfig } from '@cost-app/shared-components'

// 获取基础列配置
const baseColumns = generateColumnConfig('yuSuan')

// 添加自定义列
const customColumns = [
  ...baseColumns,
  {
    field: 'customField',
    title: '自定义字段',
    width: 120,
    editable: true
  }
]
```

## 最佳实践

1. **模块类型**: 始终明确指定 `moduleType`
2. **数据结构**: 确保数据包含必要的字段 (`id`, `kind`, `level` 等)
3. **事件处理**: 合理处理各种事件，特别是 `componentAction`
4. **性能优化**: 大数据量时使用虚拟滚动
5. **错误处理**: 为异步操作添加错误处理

## 故障排除

### 常见问题

1. **列不显示**: 检查 `moduleType` 是否正确
2. **编辑失效**: 检查数据中的 `isLocked` 字段
3. **菜单异常**: 检查 `kind` 字段是否符合规范
4. **组件加载失败**: 检查模块组件是否正确导入

### 调试技巧

```javascript
// 开启调试模式
const debugMode = true

// 监听所有事件
if (debugMode) {
  const events = ['dataChange', 'rowSelect', 'cellEdit', 'componentAction']
  events.forEach(event => {
    console.log(`${event}:`, data)
  })
}
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持五个造价模块
- 实现配置驱动架构
- 添加模块特有组件支持
