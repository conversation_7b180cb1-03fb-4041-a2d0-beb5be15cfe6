<template>
  <div class="tab-page-content material-summary">
    <div class="page-header">
      <h3>{{ pageTitle }}</h3>
      <div class="page-actions">
        <a-button @click="handleCalculate">
          <template #icon><CalculatorOutlined /></template>
          重新汇总
        </a-button>
        <a-button @click="handleExport">
          <template #icon><ExportOutlined /></template>
          导出汇总表
        </a-button>
      </div>
    </div>

    <div class="page-body">
      <!-- 汇总统计 -->
      <a-card title="人材机费用汇总" class="summary-card">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-statistic
              title="人工费"
              :value="summaryData.totalLabor"
              :precision="2"
              suffix="元"
              :value-style="{ color: '#3f8600' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="材料费"
              :value="summaryData.totalMaterial"
              :precision="2"
              suffix="元"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="机械费"
              :value="summaryData.totalMechanical"
              :precision="2"
              suffix="元"
              :value-style="{ color: '#722ed1' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="合计费用"
              :value="summaryData.totalAmount"
              :precision="2"
              suffix="元"
              :value-style="{ color: '#cf1322' }"
            />
          </a-col>
        </a-row>
      </a-card>

      <!-- 详细表格 -->
      <a-card title="详细清单" class="detail-card">
        <CostTable
          :data="tableData"
          :columns="tableColumns"
          :editable="false"
          :show-toolbar="true"
          :show-summary="true"
          :scroll-config="{ x: 1200, y: 400 }"
          :table-type="moduleType"
          row-key="id"
          @row-select="handleRowSelect"
        >
          <template #toolbar-right>
            <a-space>
              <a-button size="small" @click="handleRefresh">
                <template #icon><ReloadOutlined /></template>
                刷新
              </a-button>
            </a-space>
          </template>
        </CostTable>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  CalculatorOutlined,
  ExportOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'
import CostTable from '../CostTable.vue'

const props = defineProps({
  moduleType: {
    type: String,
    required: true
  },
  levelType: {
    type: Number,
    required: true
  },
  tabInfo: {
    type: Object,
    default: () => ({})
  },
  projectData: {
    type: Object,
    default: () => ({})
  },
  tableData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 汇总数据
const summaryData = ref({
  totalLabor: 0,
  totalMaterial: 0,
  totalMechanical: 0,
  totalAmount: 0
})

// 表格数据
const tableData = ref([])

// 计算属性
const pageTitle = computed(() => {
  if (props.moduleType === 'settlement') {
    return '人材机调整'
  }
  return '人材机汇总'
})

// 表格列配置
const tableColumns = [
  {
    title: '序号',
    field: 'index',
    dataIndex: 'index',
    width: 80,
    align: 'center'
  },
  {
    title: '项目名称',
    field: 'name',
    dataIndex: 'name',
    width: 200,
    align: 'left'
  },
  {
    title: '单位',
    field: 'unit',
    dataIndex: 'unit',
    width: 80,
    align: 'center'
  },
  {
    title: '数量',
    field: 'quantity',
    dataIndex: 'quantity',
    width: 100,
    align: 'right',
    formatter: (value) => Number(value || 0).toFixed(3)
  },
  {
    title: '人工费单价',
    field: 'laborUnitPrice',
    dataIndex: 'laborUnitPrice',
    width: 120,
    align: 'right',
    formatter: (value) => Number(value || 0).toFixed(2)
  },
  {
    title: '人工费',
    field: 'laborCost',
    dataIndex: 'laborCost',
    width: 120,
    align: 'right',
    formatter: (value) => Number(value || 0).toFixed(2)
  },
  {
    title: '材料费单价',
    field: 'materialUnitPrice',
    dataIndex: 'materialUnitPrice',
    width: 120,
    align: 'right',
    formatter: (value) => Number(value || 0).toFixed(2)
  },
  {
    title: '材料费',
    field: 'materialCost',
    dataIndex: 'materialCost',
    width: 120,
    align: 'right',
    formatter: (value) => Number(value || 0).toFixed(2)
  },
  {
    title: '机械费单价',
    field: 'mechanicalUnitPrice',
    dataIndex: 'mechanicalUnitPrice',
    width: 120,
    align: 'right',
    formatter: (value) => Number(value || 0).toFixed(2)
  },
  {
    title: '机械费',
    field: 'mechanicalCost',
    dataIndex: 'mechanicalCost',
    width: 120,
    align: 'right',
    formatter: (value) => Number(value || 0).toFixed(2)
  },
  {
    title: '合计',
    field: 'totalCost',
    dataIndex: 'totalCost',
    width: 150,
    align: 'right',
    formatter: (value) => Number(value || 0).toFixed(2)
  }
]

// 事件处理
const handleCalculate = () => {
  calculateSummary()
  message.success('汇总计算完成')
  emit('action', 'calculate', summaryData.value)
}

const handleExport = () => {
  emit('action', 'export', {
    type: 'material-summary',
    data: { summary: summaryData.value, details: tableData.value }
  })
  message.success('导出成功')
}

const handleRefresh = () => {
  initializeData()
  message.success('数据已刷新')
  emit('action', 'refresh')
}

const handleRowSelect = (selectedKeys, selectedRows) => {
  emit('action', 'rowSelect', { selectedKeys, selectedRows })
}

// 计算汇总数据
const calculateSummary = () => {
  const totals = tableData.value.reduce((acc, item) => {
    acc.totalLabor += item.laborCost || 0
    acc.totalMaterial += item.materialCost || 0
    acc.totalMechanical += item.mechanicalCost || 0
    acc.totalAmount += item.totalCost || 0
    return acc
  }, {
    totalLabor: 0,
    totalMaterial: 0,
    totalMechanical: 0,
    totalAmount: 0
  })

  summaryData.value = totals
}

// 处理数据，转换为人材机汇总格式
const processTableData = (rawData) => {
  const flattenData = (items) => {
    let result = []
    items.forEach(item => {
      if (!item.children || item.children.length === 0) {
        // 叶子节点才计入汇总
        result.push({
          id: item.id,
          name: item.name,
          unit: item.unit || '项',
          quantity: item.quantity || 0,
          laborUnitPrice: item.laborUnitPrice || 0,
          laborCost: (item.quantity || 0) * (item.laborUnitPrice || 0),
          materialUnitPrice: item.materialUnitPrice || 0,
          materialCost: (item.quantity || 0) * (item.materialUnitPrice || 0),
          mechanicalUnitPrice: item.mechanicalUnitPrice || 0,
          mechanicalCost: (item.quantity || 0) * (item.mechanicalUnitPrice || 0),
          totalCost: 0
        })
      }
      if (item.children && item.children.length > 0) {
        result = result.concat(flattenData(item.children))
      }
    })
    return result
  }

  const processed = flattenData(rawData)

  // 计算总费用
  processed.forEach((item, index) => {
    item.index = index + 1
    item.totalCost = item.laborCost + item.materialCost + item.mechanicalCost
  })

  return processed
}

// 初始化数据
const initializeData = () => {
  if (props.tableData && props.tableData.length > 0) {
    tableData.value = processTableData(props.tableData)
    calculateSummary()
  }
}

// 监听数据变化
watch(() => props.tableData, () => {
  initializeData()
}, { deep: true })

onMounted(() => {
  initializeData()
})
</script>

<style lang="scss" scoped>
.material-summary {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      color: #333;
      font-weight: 600;
    }

    .page-actions {
      display: flex;
      gap: 8px;
    }
  }

  .page-body {
    flex: 1;
    overflow-y: auto;

    .summary-card {
      margin-bottom: 16px;

      :deep(.ant-statistic-content) {
        font-size: 18px;
      }
    }

    .detail-card {
      :deep(.ant-table-wrapper) {
        .ant-table-tbody > tr > td {
          padding: 8px 16px;
        }
      }
    }
  }
}
</style>