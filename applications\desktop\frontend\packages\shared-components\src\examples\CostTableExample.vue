<!--
  CostTable 使用示例
  展示如何在不同模块中使用统一的CostTable组件
-->
<template>
  <div class="cost-table-example">
    <div class="example-header">
      <h2>CostTable 模块化示例</h2>
      <a-space>
        <a-select
          v-model:value="currentModule"
          style="width: 120px"
          @change="handleModuleChange"
        >
          <a-select-option value="gaiSuan">概算</a-select-option>
          <a-select-option value="yuSuan">预算</a-select-option>
          <a-select-option value="jieSuan">结算</a-select-option>
          <a-select-option value="shenHe">审核</a-select-option>
          <a-select-option value="gongLiaoJi">工料机</a-select-option>
        </a-select>
        <a-button @click="loadSampleData">加载示例数据</a-button>
        <a-button @click="clearData">清空数据</a-button>
      </a-space>
    </div>

    <div class="example-content">
      <!-- 统一的CostTable组件 -->
      <CostTable
        :data="tableData"
        :module-type="currentModule"
        :editable="true"
        :range-selection="true"
        :bordered="true"
        table-size="small"
        :scroll-config="{ x: 1200, y: 'calc(100vh - 300px)' }"
        @data-change="handleDataChange"
        @row-select="handleRowSelect"
        @cell-edit="handleCellEdit"
        @cell-edited="handleCellEdited"
        @add-row="handleAddRow"
        @delete-row="handleDeleteRow"
        @copy-rows="handleCopyRows"
        @paste-rows="handlePasteRows"
        @component-action="handleComponentAction"
        @record-update="handleRecordUpdate"
      />
    </div>

    <!-- 操作日志 -->
    <div class="example-footer">
      <a-card title="操作日志" size="small" style="height: 200px">
        <div class="log-content">
          <div
            v-for="(log, index) in operationLogs"
            :key="index"
            class="log-item"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action">{{ log.action }}</span>
            <span class="log-detail">{{ log.detail }}</span>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { CostTable, getModuleConfig } from '@cost-app/shared-components'

// 响应式数据
const currentModule = ref('yuSuan')
const tableData = ref([])
const operationLogs = ref([])

// 计算属性
const moduleConfig = computed(() => getModuleConfig(currentModule.value))

// 示例数据生成器
const generateSampleData = (moduleType) => {
  const config = getModuleConfig(moduleType)
  const baseData = [
    {
      id: 1,
      kind: '01',
      [config.codeField]: '01',
      [config.nameField]: '土建工程',
      level: 0,
      children: [
        {
          id: 2,
          kind: '02',
          [config.codeField]: '0101',
          [config.nameField]: '基础工程',
          level: 1,
          children: [
            {
              id: 3,
              kind: moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi' ? '-1' : '03',
              [config.codeField]: moduleType === 'gaiSuan' ? 'A1-1' : '010101001',
              [config.nameField]: '挖土方',
              unit: 'm³',
              quantity: 100,
              price: 25.5,
              amount: 2550,
              level: 2
            }
          ]
        }
      ]
    },
    {
      id: 4,
      kind: '01',
      [config.codeField]: '02',
      [config.nameField]: '装饰工程',
      level: 0,
      children: [
        {
          id: 5,
          kind: '02',
          [config.codeField]: '0201',
          [config.nameField]: '墙面装饰',
          level: 1,
          children: [
            {
              id: 6,
              kind: moduleType === 'gaiSuan' || moduleType === 'gongLiaoJi' ? '-1' : '03',
              [config.codeField]: moduleType === 'gaiSuan' ? 'A2-1' : '020101001',
              [config.nameField]: '内墙涂料',
              unit: 'm²',
              quantity: 500,
              price: 15.8,
              amount: 7900,
              level: 2
            }
          ]
        }
      ]
    }
  ]

  // 根据模块特性添加特定字段
  if (config.features.hasProjectAttr) {
    baseData.forEach(item => {
      if (item.children) {
        item.children.forEach(child => {
          if (child.children) {
            child.children.forEach(grandChild => {
              grandChild.projectAttr = '标准做法'
            })
          }
        })
      }
    })
  }

  if (config.features.hasSpecification) {
    baseData.forEach(item => {
      if (item.children) {
        item.children.forEach(child => {
          if (child.children) {
            child.children.forEach(grandChild => {
              grandChild.specification = '按设计要求'
            })
          }
        })
      }
    })
  }

  return baseData
}

// 方法
const handleModuleChange = (newModule) => {
  addLog('模块切换', `切换到${getModuleConfig(newModule).name}模块`)
  loadSampleData()
}

const loadSampleData = () => {
  tableData.value = generateSampleData(currentModule.value)
  addLog('数据加载', `加载${moduleConfig.value.name}示例数据`)
}

const clearData = () => {
  tableData.value = []
  addLog('数据清空', '清空所有数据')
}

const handleDataChange = (data) => {
  addLog('数据变更', `数据发生变更，共${data.length}条记录`)
}

const handleRowSelect = (selectedRows) => {
  addLog('行选择', `选中${selectedRows.length}行`)
}

const handleCellEdit = (editData) => {
  addLog('单元格编辑', `编辑字段: ${editData.field}`)
}

const handleCellEdited = (editData) => {
  addLog('单元格编辑完成', `${editData.field}: ${editData.oldValue} → ${editData.newValue}`)
}

const handleAddRow = (addData) => {
  addLog('新增行', `新增${addData.type || '行'}`)
}

const handleDeleteRow = (record) => {
  addLog('删除行', `删除: ${record[moduleConfig.value.nameField]}`)
}

const handleCopyRows = (rows) => {
  addLog('复制行', `复制${rows.length}行`)
}

const handlePasteRows = (pasteData) => {
  addLog('粘贴行', `粘贴${pasteData.rows.length}行`)
}

const handleComponentAction = (actionData) => {
  addLog('组件操作', `执行操作: ${actionData.type}`)
}

const handleRecordUpdate = (updateData) => {
  addLog('记录更新', `更新字段: ${updateData.field}`)
}

// 添加操作日志
const addLog = (action, detail) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  operationLogs.value.unshift({
    time,
    action,
    detail
  })
  
  // 保持最多50条日志
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50)
  }
}

// 初始化
loadSampleData()
</script>

<style scoped>
.cost-table-example {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.example-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.example-header h2 {
  margin: 0;
  color: #1890ff;
}

.example-content {
  flex: 1;
  min-height: 0;
  margin-bottom: 16px;
}

.example-footer {
  height: 200px;
}

.log-content {
  height: 150px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-item {
  display: flex;
  margin-bottom: 4px;
  padding: 2px 0;
  border-bottom: 1px solid #f5f5f5;
}

.log-time {
  color: #999;
  width: 80px;
  flex-shrink: 0;
}

.log-action {
  color: #1890ff;
  width: 100px;
  flex-shrink: 0;
  font-weight: 500;
}

.log-detail {
  color: #666;
  flex: 1;
}
</style>
