<template>
  <div class="tab-page-content project-overview">
    <div class="page-header">
      <h3>{{ pageTitle }}</h3>
      <div class="page-actions">
        <a-button type="primary" @click="handleSave">
          <template #icon><SaveOutlined /></template>
          保存
        </a-button>
        <a-button @click="handleExport">
          <template #icon><ExportOutlined /></template>
          导出
        </a-button>
      </div>
    </div>

    <div class="page-body">
      <a-card title="基本信息" class="info-card">
        <a-form
          :model="formData"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
          layout="horizontal"
        >
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="项目名称">
                <a-input
                  v-model:value="formData.projectName"
                  placeholder="请输入项目名称"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="项目编码">
                <a-input
                  v-model:value="formData.projectCode"
                  placeholder="请输入项目编码"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="建设单位">
                <a-input
                  v-model:value="formData.constructionUnit"
                  placeholder="请输入建设单位"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="设计单位">
                <a-input
                  v-model:value="formData.designUnit"
                  placeholder="请输入设计单位"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="建设规模">
                <a-input
                  v-model:value="formData.constructionScale"
                  placeholder="请输入建设规模"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="计价模式">
                <a-select
                  v-model:value="formData.pricingMode"
                  placeholder="请选择计价模式"
                >
                  <a-select-option value="fixed">固定价格</a-select-option>
                  <a-select-option value="adjustable">可调价格</a-select-option>
                  <a-select-option value="cost_plus">成本加酬金</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24" v-if="moduleType !== 'settlement'">
            <a-col :span="12">
              <a-form-item :label="amountLabel">
                <a-input-number
                  v-model:value="formData.totalAmount"
                  :formatter="value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
                  :parser="value => value.replace(/¥\s?|(,*)/g, '')"
                  style="width: 100%"
                  placeholder="请输入金额"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="编制日期">
                <a-date-picker
                  v-model:value="formData.compileDate"
                  style="width: 100%"
                  placeholder="请选择编制日期"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="24">
            <a-col :span="24">
              <a-form-item label="项目描述">
                <a-textarea
                  v-model:value="formData.description"
                  :rows="4"
                  placeholder="请输入项目描述"
                />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </a-card>

      <!-- 模块特定内容 -->
      <a-card title="项目统计" class="stats-card" v-if="showStats">
        <a-row :gutter="24">
          <a-col :span="6">
            <a-statistic
              :title="`${moduleLabel}项目数`"
              :value="stats.projectCount"
              :value-style="{ color: '#3f8600' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              :title="`总${amountLabel}`"
              :value="stats.totalAmount"
              :precision="2"
              suffix="万元"
              :value-style="{ color: '#cf1322' }"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="已完成项目"
              :value="stats.completedCount"
              suffix="项"
            />
          </a-col>
          <a-col :span="6">
            <a-statistic
              title="进行中项目"
              :value="stats.inProgressCount"
              suffix="项"
              :value-style="{ color: '#1890ff' }"
            />
          </a-col>
        </a-row>
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { SaveOutlined, ExportOutlined } from '@ant-design/icons-vue'
import dayjs from 'dayjs'

const props = defineProps({
  moduleType: {
    type: String,
    required: true
  },
  levelType: {
    type: Number,
    required: true
  },
  tabInfo: {
    type: Object,
    default: () => ({})
  },
  projectData: {
    type: Object,
    default: () => ({})
  },
  tableData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 表单数据
const formData = ref({
  projectName: '',
  projectCode: '',
  constructionUnit: '',
  designUnit: '',
  constructionScale: '',
  pricingMode: 'fixed',
  totalAmount: null,
  compileDate: null,
  description: ''
})

// 统计数据
const stats = ref({
  projectCount: 0,
  totalAmount: 0,
  completedCount: 0,
  inProgressCount: 0
})

// 计算属性
const pageTitle = computed(() => {
  if (props.levelType === 1) {
    return '项目概况'
  } else if (props.levelType === 3 && props.moduleType === 'settlement') {
    return '项目概况'
  }
  return '工程概况'
})

const moduleLabel = computed(() => {
  const labels = {
    'budget': '预算',
    'rough-estimate': '概算',
    'settlement': '结算',
    'material': '工料机'
  }
  return labels[props.moduleType] || '预算'
})

const amountLabel = computed(() => {
  const labels = {
    'budget': '预算金额',
    'rough-estimate': '概算金额',
    'settlement': '结算金额',
    'material': '工料机金额'
  }
  return labels[props.moduleType] || '金额'
})

const showStats = computed(() => {
  return props.levelType === 1 || (props.levelType === 3 && props.tableData.length > 0)
})

// 方法
const handleSave = () => {
  emit('action', 'save', formData.value)
  message.success('保存成功')
}

const handleExport = () => {
  emit('action', 'export', {
    type: 'project-overview',
    data: formData.value
  })
  message.success('导出成功')
}

// 计算统计数据
const calculateStats = () => {
  if (props.tableData && props.tableData.length > 0) {
    stats.value.projectCount = props.tableData.length
    stats.value.totalAmount = props.tableData.reduce((sum, item) => sum + (item.amount || 0), 0) / 10000
    stats.value.completedCount = props.tableData.filter(item => item.status === 'completed').length
    stats.value.inProgressCount = props.tableData.filter(item => item.status === 'in-progress').length
  }
}

// 初始化数据
const initializeData = () => {
  if (props.projectData && Object.keys(props.projectData).length > 0) {
    Object.assign(formData.value, props.projectData)
  }

  // 设置默认编制日期
  if (!formData.value.compileDate) {
    formData.value.compileDate = dayjs()
  }
}

// 监听数据变化
watch(() => props.projectData, () => {
  initializeData()
}, { deep: true })

watch(() => props.tableData, () => {
  calculateStats()
}, { deep: true })

watch(formData, (newData) => {
  emit('dataChange', newData)
}, { deep: true })

onMounted(() => {
  initializeData()
  calculateStats()
})
</script>

<style lang="scss" scoped>
.project-overview {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 16px;

    h3 {
      margin: 0;
      color: #333;
      font-weight: 600;
    }

    .page-actions {
      display: flex;
      gap: 8px;
    }
  }

  .page-body {
    flex: 1;
    overflow-y: auto;
    padding-right: 8px;

    .info-card,
    .stats-card {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .stats-card {
      :deep(.ant-statistic-content) {
        font-size: 20px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .project-overview {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }
  }
}
</style>