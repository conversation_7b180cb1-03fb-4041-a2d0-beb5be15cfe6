<!--
  概算模块 - 引入工程量组件
  占位符组件，待后续实现
-->
<template>
  <a-modal
    v-model:open="visible"
    title="引入工程量"
    width="800px"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="introduction-quantity">
      <a-result
        status="info"
        title="功能开发中"
        sub-title="引入工程量功能正在开发中，敬请期待"
      >
        <template #icon>
          <CalculatorOutlined style="color: #1890ff" />
        </template>
      </a-result>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { CalculatorOutlined } from '@ant-design/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  record: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

// 响应式数据
const visible = ref(props.visible)

// 监听 visible 变化
watch(() => props.visible, (newVal) => {
  visible.value = newVal
})

watch(visible, (newVal) => {
  emit('update:visible', newVal)
})

// 方法
const handleConfirm = () => {
  emit('confirm', { type: 'introductionQuantity', data: {} })
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
  emit('cancel')
}
</script>

<style scoped>
.introduction-quantity {
  text-align: center;
  padding: 20px;
}
</style>
