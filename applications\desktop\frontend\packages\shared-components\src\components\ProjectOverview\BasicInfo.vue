<template>
  <div class="basic-info">
    <!-- 工具栏 -->
    <div v-if="showToolbar" class="toolbar">
      <a-space>
        <a-button
          type="primary"
          size="small"
          @click="handleAdd"
          :disabled="!editable"
        >
          <template #icon><PlusOutlined /></template>
          新增
        </a-button>

        <a-button
          size="small"
          @click="handleDelete"
          :disabled="!canDelete"
        >
          <template #icon><DeleteOutlined /></template>
          删除
        </a-button>

        <a-button
          size="small"
          @click="handleRefresh"
        >
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </a-space>
    </div>

    <!-- 表格容器 -->
    <div class="table-container">
      <s-table
        ref="tableRef"
        :columns="tableColumns"
        :data-source="internalTableData"
        :tree-config="treeConfig"
        :edit-config="editConfig"
        :row-config="rowConfig"
        :loading="loading"
        height="100%"
        border
        stripe
        show-overflow
        @current-change="handleCurrentChange"
        @edit-closed="handleEditClosed"
        @cell-click="handleCellClick"
        @cell-dblclick="handleCellDblClick"
        @context-menu="handleContextMenu"
        :row-class-name="getRowClassName"
        :cell-class-name="getCellClassName"
      >
        <!-- 序号列 -->
        <template #bodyCell_dispNo="{ record, rowIndex }">
          <span>{{ getDisplayIndex(record, rowIndex) }}</span>
        </template>

        <!-- 名称列 -->
        <template #bodyCell_name="{ record }">
          <span
            :class="{
              'title-bold': record.type === 'title' || ['基本信息', '投标信息', '招标信息', '工程所在地'].includes(record.name),
              'color-red': colorFieldList.includes(record.name)
            }"
          >
            {{ record.name }}
          </span>
        </template>

        <!-- 备注列 -->
        <template #bodyCell_remark="{ record }">
          <div
            :class="{
              'field-locked': record.lockFlag,
              'field-empty': !record.remark
            }"
          >
            <template v-if="record.lockFlag">
              <span class="field-locked">{{ record.remark || '暂无数据' }}</span>
            </template>
            <template v-else-if="isCellEditable(record)">
              <!-- 下拉选择 -->
              <a-select
                v-if="hasSelectOptions(record) && !dateSelect.includes(record.name) && !avergeList.includes(record.name.trim())"
                v-model:value="record.remark"
                :options="getSelectOptions(record)"
                placeholder="请选择"
                style="width: 100%"
                @change="(value) => handleFieldChange(record, 'remark', value)"
              />
              <!-- 日期选择 -->
              <a-date-picker
                v-else-if="dateSelect.includes(record.name)"
                v-model:value="record.remark"
                placeholder="请选择日期"
                style="width: 100%"
                @change="(value) => handleFieldChange(record, 'remark', value)"
              />
              <!-- 数字输入 -->
              <a-input-number
                v-else-if="avergeList.includes(record.name.trim())"
                v-model:value="record.remark"
                placeholder="请输入数值"
                style="width: 100%"
                @change="(value) => handleFieldChange(record, 'remark', value)"
              />
              <!-- 普通文本输入 -->
              <a-input
                v-else
                v-model:value="record.remark"
                placeholder="请输入备注"
                @blur="(e) => handleFieldChange(record, 'remark', e.target.value)"
                @pressEnter="(e) => handleFieldChange(record, 'remark', e.target.value)"
              />
            </template>
            <template v-else>
              <span>{{ record.remark || '暂无数据' }}</span>
            </template>
          </div>
        </template>
      </s-table>
    </div>

    <!-- 右键菜单 -->
    <a-dropdown
      v-model:open="contextMenuVisible"
      :trigger="['contextmenu']"
      placement="bottomLeft"
    >
      <div></div>
      <template #overlay>
        <a-menu @click="handleMenuClick">
          <a-menu-item key="add" :disabled="!editable">
            <PlusOutlined />
            新增
          </a-menu-item>
          <a-menu-item key="delete" :disabled="!canDelete">
            <DeleteOutlined />
            删除
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="copy">
            <CopyOutlined />
            复制
          </a-menu-item>
          <a-menu-item key="paste" :disabled="!canPaste">
            <FileAddOutlined />
            粘贴
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  DeleteOutlined,
  CopyOutlined,
  FileAddOutlined
} from '@ant-design/icons-vue'
import { STable } from '@surely-vue/table'
import { getBasicInfoByModule } from './mockData/basicInfoData'

const props = defineProps({
  moduleType: {
    type: String,
    default: 'budget'
  },
  levelType: {
    type: Number,
    default: 3
  },
  projectData: {
    type: Object,
    default: () => ({})
  },
  tableData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['dataChange', 'save', 'delete', 'refresh'])

// 参考原有项目的字段配置
const colorFieldList = [
  '建筑面积',
  '工程规模',
  '编制单位法定代表人',
  '招标人(发包人)',
  '招标人(发包人)法人或其授权人',
  '工程造价咨询人',
  '工程造价咨询人法人或其授权人',
  '编制人',
  '编制时间',
  '核对人(复核人)',
  '核对(复核)时间',
  '投标人(承包人)',
  '投标人(承包人)法人或其授权人',
]

const dateSelect = ['开工日期', '竣工日期', '编制时间', '核对(复核)时间']
const avergeList = ['建筑面积', '工程规模']
const notEditList = ['基本信息', '工程所在地', '招标信息', '投标信息']

// 响应式数据
const tableRef = ref()
const contextMenuVisible = ref(false)
const internalTableData = ref([])
const currentRecord = ref(null)
const canDelete = ref(false)
const canPaste = ref(false)
const showToolbar = computed(() => props.showToolbar !== false)

// 表格配置
const tableColumns = computed(() => [
  {
    field: 'dispNo',
    title: '序号',
    width: 60,
    align: 'center',
    slots: { bodyCell: 'bodyCell_dispNo' }
  },
  {
    field: 'name',
    title: '名称',
    width: 235,
    align: 'left',
    slots: { bodyCell: 'bodyCell_name' }
  },
  {
    field: 'remark',
    title: '备注',
    minWidth: 300,
    align: 'left',
    slots: { bodyCell: 'bodyCell_remark' }
  }
])

const treeConfig = computed(() => ({
  children: 'childrenList',
  indent: 20,
  showIcon: false,
  accordion: false
}))

const editConfig = computed(() => ({
  trigger: 'dblclick',
  mode: 'cell',
  showStatus: true
}))

const rowConfig = computed(() => ({
  keyField: 'sequenceNbr',
  isHover: true
}))

// 核心方法
const hasSelectOptions = (record) => {
  return record.jsonStr && Array.isArray(record.jsonStr) && record.jsonStr.length > 0
}

const getSelectOptions = (record) => {
  if (hasSelectOptions(record)) {
    return record.jsonStr.map(item => ({
      label: item,
      value: item
    }))
  }
  return []
}

const isCellEditable = (record) => {
  if (!props.editable || record.lockFlag) return false
  if (record.type === 'title') return false
  if (notEditList.includes(record.name)) return false
  return true
}

const getDisplayIndex = (record, rowIndex) => {
  if (record.type === 'title') return ''
  return record.dispNo || (rowIndex + 1)
}

const getCellClassName = ({ row, column }) => {
  let className = ''

  if (column.field === 'name' && ['基本信息', '投标信息', '招标信息', '工程所在地'].includes(row.name)) {
    className += 'title-bold '
  }

  if (column.field === 'name' && row.type === 'title') {
    className += 'title-bold '
  }

  if (column.field === 'name' && colorFieldList.includes(row.name)) {
    className += 'color-red '
  }

  return className.trim()
}

const getRowClassName = ({ row }) => {
  if (row.lockFlag == 1) {
    return 'row-lock-color'
  }
  return ''
}

// 计算属性
const canDelete = computed(() => {
  return props.editable && currentRecord.value && currentRecord.value.addFlag
})

// 字段配置相关
const getFieldConfig = (record) => {
  const moduleConfig = getModuleFieldConfig(props.moduleType)
  return getFieldConfigFromModule(record.name, moduleConfig)
}

const getEditComponent = (record) => {
  const config = getFieldConfig(record)
  
  switch (config?.type) {
    case 'date':
      return 'a-date-picker'
    case 'select':
      return 'a-select'
    case 'number':
      return 'a-input-number'
    case 'textarea':
      return 'a-textarea'
    default:
      return 'a-input'
  }
}

const getPlaceholder = (record) => {
  const config = getFieldConfig(record)
  return config?.placeholder || `请输入${record.name}`
}

// 样式类名
const getNameClass = (record) => {
  return {
    'field-title': record.type === 'title',
    'field-highlight': isHighlightField(record.name),
    'field-required': isRequiredField(record.name)
  }
}

const getRemarkClass = (record) => {
  return {
    'field-locked': record.lockFlag,
    'field-empty': !record.remark
  }
}

const getDisplayIndex = (record, rowIndex) => {
  if (record.type === 'title') return ''
  return record.dispNo || (rowIndex + 1)
}

// 字段验证和格式化
const isHighlightField = (fieldName) => {
  const highlightFields = [
    '建筑面积', '工程规模', '编制单位法定代表人',
    '招标人(发包人)', '工程造价咨询人', '编制人',
    '编制时间', '核对人(复核人)', '核对(复核)时间'
  ]
  return highlightFields.includes(fieldName)
}

const isRequiredField = (fieldName) => {
  const config = getFieldConfig({ name: fieldName })
  return config?.required || false
}

const isCellEditable = (record, column) => {
  if (!props.editable || record.lockFlag) return false
  if (column.field !== 'remark') return false
  
  // 标题行不可编辑
  if (record.type === 'title') return false
  
  // 根据模块和层级判断编辑权限
  const restrictedFields = getRestrictedFields()
  if (restrictedFields.includes(record.name)) return false
  
  return true
}

const getRestrictedFields = () => {
  const restrictions = []
  
  // 结算模块的特殊限制
  if (props.moduleType === 'settlement') {
    if (props.levelType === 1) {
      restrictions.push('工程名称', '项目编号')
    }
    if (props.levelType === 3) {
      restrictions.push('工程专业')
    }
  }
  
  return restrictions
}

const formatFieldValue = (record) => {
  const config = getFieldConfig(record)
  return formatValue(record.remark, config)
}

// 事件处理
const handleCellClick = (params) => {
  const { record } = params
  currentRecord.value = record
  selectedRecord.value = record
}

const handleCellDblClick = (params) => {
  const { record, column } = params
  if (isCellEditable(record, column)) {
    // 双击进入编辑模式
    nextTick(() => {
      tableRef.value?.setActiveCell(record, column.field)
    })
  }
}

const handleEditClosed = (params) => {
  const { record, column } = params
  handleFieldChange(record, column)
}

const handleFieldChange = (record, column) => {
  // 验证字段值
  const config = getFieldConfig(record)
  const isValid = validateField(record.remark, config)
  
  if (!isValid) {
    message.error(`${record.name}格式不正确`)
    return
  }
  
  // 触发数据变更事件
  emit('dataChange', props.tableData)
  
  // 自动保存
  if (config?.autoSave !== false) {
    handleAutoSave(record)
  }
}

const handleFieldBlur = (record, column) => {
  // 失焦时格式化数值
  if (record.name === '建筑面积' || record.name === '工程规模') {
    if (record.remark && !isNaN(record.remark)) {
      record.remark = Number(record.remark).toFixed(2)
    }
  }
}

const handleAutoSave = async (record) => {
  try {
    emit('save', [record])
  } catch (error) {
    console.error('自动保存失败:', error)
  }
}

const handleContextMenu = (params) => {
  const { record } = params
  currentRecord.value = record
  contextMenuVisible.value = true
}

const handleMenuClick = ({ key }) => {
  contextMenuVisible.value = false
  
  switch (key) {
    case 'add':
      handleAdd()
      break
    case 'delete':
      handleDelete()
      break
    case 'copy':
      handleCopy()
      break
    case 'paste':
      handlePaste()
      break
  }
}

const handleAdd = () => {
  const newRecord = addNewRecord(currentRecord.value)
  emit('dataChange', [...props.tableData, newRecord])
}

const handleDelete = () => {
  if (!currentRecord.value || !currentRecord.value.addFlag) {
    message.warning('只能删除新增的记录')
    return
  }
  
  emit('delete', currentRecord.value.sequenceNbr)
}

const handleCopy = () => {
  copyRecord(currentRecord.value)
  message.success('已复制到剪贴板')
}

const handlePaste = () => {
  const newRecord = pasteRecord(currentRecord.value)
  if (newRecord) {
    emit('dataChange', [...props.tableData, newRecord])
    message.success('粘贴成功')
  }
}

const handleRefresh = () => {
  emit('refresh')
}

// 初始化数据
const initializeData = () => {
  if (!props.tableData || props.tableData.length === 0) {
    // 如果没有传入数据，使用模拟数据
    const mockData = getBasicInfoByModule(props.moduleType)
    emit('dataChange', mockData)
  }
}

// 监听器
watch(() => props.tableData, (newData) => {
  // 数据变化时更新表格
  nextTick(() => {
    tableRef.value?.loadData(newData)
  })
}, { deep: true, immediate: true })

watch(() => props.moduleType, () => {
  // 模块类型变化时重新初始化数据
  initializeData()
}, { immediate: true })

// S-Table需要的事件处理方法
const handleAdd = () => {
  message.info('新增功能')
}

const handleDelete = () => {
  message.info('删除功能')
}

const handleRefresh = () => {
  emit('refresh')
}

const handleCurrentChange = ({ row }) => {
  currentRecord.value = row
}

const handleEditClosed = ({ row, column }) => {
  console.log('编辑完成:', row, column)
}

const handleCellClick = ({ row, column }) => {
  currentRecord.value = row
}

const handleCellDblClick = ({ row, column }) => {
  console.log('双击单元格:', row, column)
}

const handleContextMenu = ({ row }) => {
  currentRecord.value = row
  contextMenuVisible.value = true
}

const handleMenuClick = ({ key }) => {
  switch (key) {
    case 'add':
      handleAdd()
      break
    case 'delete':
      handleDelete()
      break
    case 'copy':
      message.info('复制功能')
      break
    case 'paste':
      message.info('粘贴功能')
      break
  }
  contextMenuVisible.value = false
}

const handleFieldChange = (record, field, value) => {
  record[field] = value
  emit('dataChange', internalTableData.value)
}

// VXE表格需要的方法（保持兼容）
const cellBeforeEditMethod = ({ row, column }) => {
  return !row.lockFlag && row.type !== 'title' && !notEditList.includes(row.name)
}

const useCellClickEvent = (cellData) => {
  console.log('单元格点击:', cellData)
}

const keyDownHandler = (event) => {
  console.log('键盘事件:', event)
}

const contextMenuClickEvent = ({ menu, row, column }) => {
  const { code } = menu

  switch (code) {
    case 'insert':
      insertHandle(row)
      break
    case 'delete':
      deleteHandle(row)
      break
    case 'copy':
      copyHandle(row)
      break
    case 'paste':
      pasteHandle(row)
      break
  }
}

const insertHandle = (row) => {
  message.info('新增功能')
}

const deleteHandle = (row) => {
  message.info('删除功能')
}

const copyHandle = (row) => {
  message.info('复制功能')
}

const pasteHandle = (row) => {
  message.info('粘贴功能')
}

const inputFinish = (row, event, field) => {
  console.log('输入完成:', row, field, event.target.value)
}

const saveCustomInput = (value, row, field) => {
  row[field] = value
  emit('dataChange', internalTableData.value)
}

const timeSelect = (row, event) => {
  console.log('时间选择:', row, event)
}

const averageBlur = (row, event, field) => {
  console.log('建筑面积失焦:', row, field, event.target.value)
}

const limitNum = (value) => {
  // 限制数字输入
  return value.replace(/[^\d.]/g, '')
}
</script>

<style lang="scss" scoped>
.basic-info {
  height: 100%;
  display: flex;
  flex-direction: column;

  .toolbar {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
    flex-shrink: 0;
  }

  .table-container {
    flex: 1;
    overflow: hidden;
  }

  // 字段样式
  :deep(.title-bold) {
    font-weight: bold;
    background-color: #f5f5f5;
  }

  :deep(.color-red) {
    color: #de3f3f;
    font-weight: 500;
  }

  :deep(.row-lock-color) {
    background-color: #bfbfbf;

    .s-table-cell {
      color: #666;
    }
  }

  :deep(.field-locked) {
    background-color: #f0f0f0;
    color: #999;
  }

  :deep(.field-empty) {
    color: #bfbfbf;
    font-style: italic;
  }

  // S-Table样式
  :deep(.s-table) {
    .s-table-header {
      background-color: #fafafa;
    }

    .s-table-body {
      .s-table-row:hover {
        background-color: #f5f5f5;
      }

      .s-table-row.row-selected {
        background-color: #e6f7ff;
      }
    }

    .s-table-cell {
      user-select: none;
    }
  }
}
</style>
