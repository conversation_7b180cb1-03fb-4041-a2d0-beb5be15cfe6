<template>
  <div class="table-content">
    <vxe-table
      align="center"
      height="98%"
      :style="{ width: '100%', maxWidth: '100%' }"
      ref="basicInfoTable"
      :loading="loading"
      :column-config="{ resizable: true }"
      :row-config="{
        isHover: true,
        isCurrent: true,
        keyField: 'sequenceNbr',
      }"
      :data="internalTableData"
      :tree-config="{
        expandAll: true,
        children: 'childrenList',
        reserve: true,
      }"
      :edit-config="{
        trigger: 'click',
        mode: 'cell',
        beforeEditMethod: cellBeforeEditMethod,
        showIcon: false,
        showStatus: false,
      }"
      @keydown="keyDownHandler"
      @cell-click="useCellClickEvent"
      :menu-config="menuConfig"
      @menu-click="contextMenuClickEvent"
      class="table-edit-common table-no-outer-border"
      :cell-class-name="getCellClassName"
      :row-class-name="getRowClassName"
    >
      <vxe-column field="dispNo" :width="60" title="序号" align="center"></vxe-column>
      <vxe-column
        field="name"
        align="left"
        title="名称"
        :width="235"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #default="{ row }">
          <span>{{ row.name }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input
            :clearable="false"
            v-if="row.addFlag && !row.lockFlag"
            placeholder="请输入名称"
            v-model="row.name"
            type="text"
            name="name"
            @blur="inputFinish(row, $event, 'name')"
          ></vxe-input>
          <span v-else>{{ row.name }}</span>
        </template>
      </vxe-column>
      <vxe-column
        field="remark"
        align="left"
        title="备注"
        :width="300"
        :edit-render="{ autofocus: '.vxe-input--inner' }"
      >
        <template #edit="{ row }">
          <vxe-select
            v-if="ifShowSelect(row) && !avergeList.includes(row.name.trim()) && !dateSelect.includes(row.name)"
            v-model="row.remark"
            :options="getSelectOptions(row)"
            placeholder="请选择"
            @change="saveCustomInput($event, row, 'remark')"
          ></vxe-select>
          <vxe-input
            v-else-if="ifShowSelect(row) && dateSelect.includes(row.name)"
            v-model="row.remark"
            @change="timeSelect(row, { $event })"
            placeholder="日期选择"
            type="date"
          ></vxe-input>
          <vxe-input
            v-else-if="ifShowSelect(row) && avergeList.includes(row.name.trim())"
            v-model="row.remark"
            @blur="averageBlur(row, $event, 'remark')"
            @keyup="row.remark = limitNum(row.remark)"
          ></vxe-input>
          <span v-else>{{ row.remark }}</span>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<script setup>
import { onMounted, onUpdated, onActivated, ref, watch, inject, getCurrentInstance } from 'vue'
import { message, Modal } from 'ant-design-vue'
import { getBasicInfoByModule } from './mockData/basicInfoData'

const props = defineProps({
  moduleType: {
    type: String,
    default: 'budget'
  },
  levelType: {
    type: Number,
    default: 3
  },
  projectData: {
    type: Object,
    default: () => ({})
  },
  tableData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['dataChange', 'save', 'delete', 'refresh'])

// 参考原有项目的字段配置
const colorFieldList = [
  '建筑面积',
  '工程规模',
  '编制单位法定代表人',
  '招标人(发包人)',
  '招标人(发包人)法人或其授权人',
  '工程造价咨询人',
  '工程造价咨询人法人或其授权人',
  '编制人',
  '编制时间',
  '核对人(复核人)',
  '核对(复核)时间',
  '投标人(承包人)',
  '投标人(承包人)法人或其授权人',
]

const dateSelect = ['开工日期', '竣工日期', '编制时间', '核对(复核)时间']
const avergeList = ['建筑面积', '工程规模']
const notEditList = ['基本信息', '工程所在地', '招标信息', '投标信息']

// 响应式数据
const basicInfoTable = ref()
const contextMenuVisible = ref(false)
const internalTableData = ref([])

// 右键菜单配置
const menuConfig = ref({
  body: {
    options: [
      [
        { code: 'insert', name: '新增', prefixIcon: 'vxe-icon-add' },
        { code: 'delete', name: '删除', prefixIcon: 'vxe-icon-delete' }
      ],
      [
        { code: 'copy', name: '复制', prefixIcon: 'vxe-icon-copy' },
        { code: 'paste', name: '粘贴', prefixIcon: 'vxe-icon-paste' }
      ]
    ]
  }
})

// 核心方法
const ifShowSelect = (row) => {
  return !row.lockFlag && !notEditList.includes(row.name)
}

const getSelectOptions = (row) => {
  if (row.jsonStr && Array.isArray(row.jsonStr)) {
    return row.jsonStr.map(item => ({
      label: item,
      value: item
    }))
  }
  return []
}

const getCellClassName = ({ $columnIndex, row, column }) => {
  let className = ''

  if (column.field === 'name' && ['基本信息', '投标信息', '招标信息'].includes(row.name) && row.addFlag === 0) {
    className += 'title-bold '
  }

  if (column.field === 'name' && row.type === 'title') {
    className += 'title-bold '
  }

  if (column.field === 'name' && colorFieldList.includes(row.name)) {
    className += 'color-red '
  }

  return className.trim()
}

const getRowClassName = ({ row }) => {
  if (row.lockFlag == 1) {
    return 'row-lock-color'
  }
  return ''
}

// 计算属性
const canDelete = computed(() => {
  return props.editable && currentRecord.value && currentRecord.value.addFlag
})

// 字段配置相关
const getFieldConfig = (record) => {
  const moduleConfig = getModuleFieldConfig(props.moduleType)
  return getFieldConfigFromModule(record.name, moduleConfig)
}

const getEditComponent = (record) => {
  const config = getFieldConfig(record)
  
  switch (config?.type) {
    case 'date':
      return 'a-date-picker'
    case 'select':
      return 'a-select'
    case 'number':
      return 'a-input-number'
    case 'textarea':
      return 'a-textarea'
    default:
      return 'a-input'
  }
}

const getPlaceholder = (record) => {
  const config = getFieldConfig(record)
  return config?.placeholder || `请输入${record.name}`
}

// 样式类名
const getNameClass = (record) => {
  return {
    'field-title': record.type === 'title',
    'field-highlight': isHighlightField(record.name),
    'field-required': isRequiredField(record.name)
  }
}

const getRemarkClass = (record) => {
  return {
    'field-locked': record.lockFlag,
    'field-empty': !record.remark
  }
}

const getDisplayIndex = (record, rowIndex) => {
  if (record.type === 'title') return ''
  return record.dispNo || (rowIndex + 1)
}

// 字段验证和格式化
const isHighlightField = (fieldName) => {
  const highlightFields = [
    '建筑面积', '工程规模', '编制单位法定代表人',
    '招标人(发包人)', '工程造价咨询人', '编制人',
    '编制时间', '核对人(复核人)', '核对(复核)时间'
  ]
  return highlightFields.includes(fieldName)
}

const isRequiredField = (fieldName) => {
  const config = getFieldConfig({ name: fieldName })
  return config?.required || false
}

const isCellEditable = (record, column) => {
  if (!props.editable || record.lockFlag) return false
  if (column.field !== 'remark') return false
  
  // 标题行不可编辑
  if (record.type === 'title') return false
  
  // 根据模块和层级判断编辑权限
  const restrictedFields = getRestrictedFields()
  if (restrictedFields.includes(record.name)) return false
  
  return true
}

const getRestrictedFields = () => {
  const restrictions = []
  
  // 结算模块的特殊限制
  if (props.moduleType === 'settlement') {
    if (props.levelType === 1) {
      restrictions.push('工程名称', '项目编号')
    }
    if (props.levelType === 3) {
      restrictions.push('工程专业')
    }
  }
  
  return restrictions
}

const formatFieldValue = (record) => {
  const config = getFieldConfig(record)
  return formatValue(record.remark, config)
}

// 事件处理
const handleCellClick = (params) => {
  const { record } = params
  currentRecord.value = record
  selectedRecord.value = record
}

const handleCellDblClick = (params) => {
  const { record, column } = params
  if (isCellEditable(record, column)) {
    // 双击进入编辑模式
    nextTick(() => {
      tableRef.value?.setActiveCell(record, column.field)
    })
  }
}

const handleEditClosed = (params) => {
  const { record, column } = params
  handleFieldChange(record, column)
}

const handleFieldChange = (record, column) => {
  // 验证字段值
  const config = getFieldConfig(record)
  const isValid = validateField(record.remark, config)
  
  if (!isValid) {
    message.error(`${record.name}格式不正确`)
    return
  }
  
  // 触发数据变更事件
  emit('dataChange', props.tableData)
  
  // 自动保存
  if (config?.autoSave !== false) {
    handleAutoSave(record)
  }
}

const handleFieldBlur = (record, column) => {
  // 失焦时格式化数值
  if (record.name === '建筑面积' || record.name === '工程规模') {
    if (record.remark && !isNaN(record.remark)) {
      record.remark = Number(record.remark).toFixed(2)
    }
  }
}

const handleAutoSave = async (record) => {
  try {
    emit('save', [record])
  } catch (error) {
    console.error('自动保存失败:', error)
  }
}

const handleContextMenu = (params) => {
  const { record } = params
  currentRecord.value = record
  contextMenuVisible.value = true
}

const handleMenuClick = ({ key }) => {
  contextMenuVisible.value = false
  
  switch (key) {
    case 'add':
      handleAdd()
      break
    case 'delete':
      handleDelete()
      break
    case 'copy':
      handleCopy()
      break
    case 'paste':
      handlePaste()
      break
  }
}

const handleAdd = () => {
  const newRecord = addNewRecord(currentRecord.value)
  emit('dataChange', [...props.tableData, newRecord])
}

const handleDelete = () => {
  if (!currentRecord.value || !currentRecord.value.addFlag) {
    message.warning('只能删除新增的记录')
    return
  }
  
  emit('delete', currentRecord.value.sequenceNbr)
}

const handleCopy = () => {
  copyRecord(currentRecord.value)
  message.success('已复制到剪贴板')
}

const handlePaste = () => {
  const newRecord = pasteRecord(currentRecord.value)
  if (newRecord) {
    emit('dataChange', [...props.tableData, newRecord])
    message.success('粘贴成功')
  }
}

const handleRefresh = () => {
  emit('refresh')
}

// 初始化数据
const initializeData = () => {
  if (!props.tableData || props.tableData.length === 0) {
    // 如果没有传入数据，使用模拟数据
    const mockData = getBasicInfoByModule(props.moduleType)
    emit('dataChange', mockData)
  }
}

// 监听器
watch(() => props.tableData, (newData) => {
  // 数据变化时更新表格
  nextTick(() => {
    tableRef.value?.loadData(newData)
  })
}, { deep: true, immediate: true })

watch(() => props.moduleType, () => {
  // 模块类型变化时重新初始化数据
  initializeData()
}, { immediate: true })

// VXE表格需要的方法
const cellBeforeEditMethod = ({ row, column }) => {
  return !row.lockFlag && row.type !== 'title' && !notEditList.includes(row.name)
}

const useCellClickEvent = (cellData) => {
  console.log('单元格点击:', cellData)
}

const keyDownHandler = (event) => {
  console.log('键盘事件:', event)
}

const contextMenuClickEvent = ({ menu, row, column }) => {
  const { code } = menu

  switch (code) {
    case 'insert':
      insertHandle(row)
      break
    case 'delete':
      deleteHandle(row)
      break
    case 'copy':
      copyHandle(row)
      break
    case 'paste':
      pasteHandle(row)
      break
  }
}

const insertHandle = (row) => {
  message.info('新增功能')
}

const deleteHandle = (row) => {
  message.info('删除功能')
}

const copyHandle = (row) => {
  message.info('复制功能')
}

const pasteHandle = (row) => {
  message.info('粘贴功能')
}

const inputFinish = (row, event, field) => {
  console.log('输入完成:', row, field, event.target.value)
}

const saveCustomInput = (value, row, field) => {
  row[field] = value
  emit('dataChange', internalTableData.value)
}

const timeSelect = (row, event) => {
  console.log('时间选择:', row, event)
}

const averageBlur = (row, event, field) => {
  console.log('建筑面积失焦:', row, field, event.target.value)
}

const limitNum = (value) => {
  // 限制数字输入
  return value.replace(/[^\d.]/g, '')
}
</script>

<style lang="scss" scoped>
.table-content {
  height: calc(100%);
}

.table-content :deep(.vxe-table) {
  .title-bold {
    font-weight: bold;
  }

  .color-red {
    color: #de3f3f;
  }

  .row-lock-color {
    background-color: #bfbfbf;
  }

  .vxe-cell .vxe-cell--label {
    user-select: none;
  }
}
</style>
