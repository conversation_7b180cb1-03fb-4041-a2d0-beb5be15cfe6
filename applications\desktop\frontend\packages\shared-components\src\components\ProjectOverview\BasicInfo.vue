<template>
  <div class="basic-info">
    <div class="toolbar" v-if="showToolbar">
      <a-space>
        <a-button @click="handleAdd" :disabled="!editable" size="small">
          <template #icon><PlusOutlined /></template>
          新增
        </a-button>
        <a-button @click="handleRefresh" size="small">
          <template #icon><ReloadOutlined /></template>
          刷新
        </a-button>
      </a-space>
    </div>

    <div class="table-container">
      <s-table
        ref="tableRef"
        :columns="tableColumns"
        :data-source="tableData"
        :loading="loading"
        :tree-config="treeConfig"
        :edit-config="editConfig"
        :row-config="rowConfig"
        :scroll-y="{ enabled: true }"
        @cell-click="handleCellClick"
        @cell-dblclick="handleCellDblClick"
        @edit-closed="handleEditClosed"
        @context-menu="handleContextMenu"
      >
        <!-- 序号列 -->
        <template #bodyCell_dispNo="{ record, rowIndex }">
          <span>{{ getDisplayIndex(record, rowIndex) }}</span>
        </template>

        <!-- 名称列 -->
        <template #bodyCell_name="{ record }">
          <span :class="getNameClass(record)">{{ record.name }}</span>
        </template>

        <!-- 备注列（可编辑） -->
        <template #bodyCell_remark="{ record, column }">
          <template v-if="isCellEditable(record, column)">
            <component 
              :is="getEditComponent(record)"
              v-model:value="record.remark"
              :config="getFieldConfig(record)"
              :placeholder="getPlaceholder(record)"
              @change="handleFieldChange(record, column)"
              @blur="handleFieldBlur(record, column)"
            />
          </template>
          <template v-else>
            <span :class="getRemarkClass(record)">
              {{ formatFieldValue(record) }}
            </span>
          </template>
        </template>
      </s-table>
    </div>

    <!-- 右键菜单 -->
    <a-dropdown
      v-model:open="contextMenuVisible"
      :trigger="['contextmenu']"
      placement="bottomLeft"
    >
      <div></div>
      <template #overlay>
        <a-menu @click="handleMenuClick">
          <a-menu-item key="add" :disabled="!editable">
            <PlusOutlined />
            新增
          </a-menu-item>
          <a-menu-item key="delete" :disabled="!canDelete">
            <DeleteOutlined />
            删除
          </a-menu-item>
          <a-menu-divider />
          <a-menu-item key="copy">
            <CopyOutlined />
            复制
          </a-menu-item>
          <a-menu-item key="paste" :disabled="!canPaste">
            <FileAddOutlined />
            粘贴
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>

<script setup>
import { ref, computed, inject, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import {
  PlusOutlined,
  ReloadOutlined,
  DeleteOutlined,
  CopyOutlined,
  FileAddOutlined
} from '@ant-design/icons-vue'
import { useBasicInfo } from './composables/useBasicInfo'
import { getFieldConfig as getFieldConfigFromModule, getModuleFieldConfig } from './configs/fieldConfigs'
import { formatValue, validateField } from './utils/fieldUtils'

const props = defineProps({
  moduleType: String,
  levelType: Number,
  projectData: Object,
  tableData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: true
  },
  showToolbar: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['dataChange', 'save', 'delete', 'refresh'])

// 注入上下文
const context = inject('projectOverviewContext', {})

// 组合式API
const {
  selectedRecord,
  clipboard,
  addNewRecord,
  deleteRecord,
  copyRecord,
  pasteRecord,
  canPaste
} = useBasicInfo()

// 响应式数据
const tableRef = ref()
const contextMenuVisible = ref(false)
const currentRecord = ref(null)

// 表格配置
const tableColumns = computed(() => [
  { 
    field: 'dispNo', 
    title: '序号', 
    width: 60, 
    align: 'center',
    slots: { bodyCell: 'bodyCell_dispNo' }
  },
  { 
    field: 'name', 
    title: '名称', 
    width: 200, 
    align: 'left',
    slots: { bodyCell: 'bodyCell_name' }
  },
  { 
    field: 'remark', 
    title: '备注', 
    minWidth: 300, 
    align: 'left',
    slots: { bodyCell: 'bodyCell_remark' }
  }
])

const treeConfig = computed(() => ({
  children: 'childrenList',
  indent: 20,
  showIcon: false,
  accordion: false
}))

const editConfig = computed(() => ({
  trigger: 'dblclick',
  mode: 'cell',
  showStatus: true
}))

const rowConfig = computed(() => ({
  keyField: 'sequenceNbr',
  isHover: true
}))

// 计算属性
const canDelete = computed(() => {
  return props.editable && currentRecord.value && currentRecord.value.addFlag
})

// 字段配置相关
const getFieldConfig = (record) => {
  const moduleConfig = getModuleFieldConfig(props.moduleType)
  return getFieldConfigFromModule(record.name, moduleConfig)
}

const getEditComponent = (record) => {
  const config = getFieldConfig(record)
  
  switch (config?.type) {
    case 'date':
      return 'a-date-picker'
    case 'select':
      return 'a-select'
    case 'number':
      return 'a-input-number'
    case 'textarea':
      return 'a-textarea'
    default:
      return 'a-input'
  }
}

const getPlaceholder = (record) => {
  const config = getFieldConfig(record)
  return config?.placeholder || `请输入${record.name}`
}

// 样式类名
const getNameClass = (record) => {
  return {
    'field-title': record.type === 'title',
    'field-highlight': isHighlightField(record.name),
    'field-required': isRequiredField(record.name)
  }
}

const getRemarkClass = (record) => {
  return {
    'field-locked': record.lockFlag,
    'field-empty': !record.remark
  }
}

const getDisplayIndex = (record, rowIndex) => {
  if (record.type === 'title') return ''
  return record.dispNo || (rowIndex + 1)
}

// 字段验证和格式化
const isHighlightField = (fieldName) => {
  const highlightFields = [
    '建筑面积', '工程规模', '编制单位法定代表人',
    '招标人(发包人)', '工程造价咨询人', '编制人',
    '编制时间', '核对人(复核人)', '核对(复核)时间'
  ]
  return highlightFields.includes(fieldName)
}

const isRequiredField = (fieldName) => {
  const config = getFieldConfig({ name: fieldName })
  return config?.required || false
}

const isCellEditable = (record, column) => {
  if (!props.editable || record.lockFlag) return false
  if (column.field !== 'remark') return false
  
  // 标题行不可编辑
  if (record.type === 'title') return false
  
  // 根据模块和层级判断编辑权限
  const restrictedFields = getRestrictedFields()
  if (restrictedFields.includes(record.name)) return false
  
  return true
}

const getRestrictedFields = () => {
  const restrictions = []
  
  // 结算模块的特殊限制
  if (props.moduleType === 'settlement') {
    if (props.levelType === 1) {
      restrictions.push('工程名称', '项目编号')
    }
    if (props.levelType === 3) {
      restrictions.push('工程专业')
    }
  }
  
  return restrictions
}

const formatFieldValue = (record) => {
  const config = getFieldConfig(record)
  return formatValue(record.remark, config)
}

// 事件处理
const handleCellClick = (params) => {
  const { record } = params
  currentRecord.value = record
  selectedRecord.value = record
}

const handleCellDblClick = (params) => {
  const { record, column } = params
  if (isCellEditable(record, column)) {
    // 双击进入编辑模式
    nextTick(() => {
      tableRef.value?.setActiveCell(record, column.field)
    })
  }
}

const handleEditClosed = (params) => {
  const { record, column } = params
  handleFieldChange(record, column)
}

const handleFieldChange = (record, column) => {
  // 验证字段值
  const config = getFieldConfig(record)
  const isValid = validateField(record.remark, config)
  
  if (!isValid) {
    message.error(`${record.name}格式不正确`)
    return
  }
  
  // 触发数据变更事件
  emit('dataChange', props.tableData)
  
  // 自动保存
  if (config?.autoSave !== false) {
    handleAutoSave(record)
  }
}

const handleFieldBlur = (record, column) => {
  // 失焦时格式化数值
  if (record.name === '建筑面积' || record.name === '工程规模') {
    if (record.remark && !isNaN(record.remark)) {
      record.remark = Number(record.remark).toFixed(2)
    }
  }
}

const handleAutoSave = async (record) => {
  try {
    emit('save', [record])
  } catch (error) {
    console.error('自动保存失败:', error)
  }
}

const handleContextMenu = (params) => {
  const { record } = params
  currentRecord.value = record
  contextMenuVisible.value = true
}

const handleMenuClick = ({ key }) => {
  contextMenuVisible.value = false
  
  switch (key) {
    case 'add':
      handleAdd()
      break
    case 'delete':
      handleDelete()
      break
    case 'copy':
      handleCopy()
      break
    case 'paste':
      handlePaste()
      break
  }
}

const handleAdd = () => {
  const newRecord = addNewRecord(currentRecord.value)
  emit('dataChange', [...props.tableData, newRecord])
}

const handleDelete = () => {
  if (!currentRecord.value || !currentRecord.value.addFlag) {
    message.warning('只能删除新增的记录')
    return
  }
  
  emit('delete', currentRecord.value.sequenceNbr)
}

const handleCopy = () => {
  copyRecord(currentRecord.value)
  message.success('已复制到剪贴板')
}

const handlePaste = () => {
  const newRecord = pasteRecord(currentRecord.value)
  if (newRecord) {
    emit('dataChange', [...props.tableData, newRecord])
    message.success('粘贴成功')
  }
}

const handleRefresh = () => {
  emit('refresh')
}

// 监听器
watch(() => props.tableData, (newData) => {
  // 数据变化时更新表格
  nextTick(() => {
    tableRef.value?.loadData(newData)
  })
}, { deep: true })
</script>

<style lang="scss" scoped>
.basic-info {
  height: 100%;
  display: flex;
  flex-direction: column;

  .toolbar {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 8px;
  }

  .table-container {
    flex: 1;
    overflow: hidden;
  }

  // 字段样式
  :deep(.field-title) {
    font-weight: bold;
    background-color: #f5f5f5;
  }

  :deep(.field-highlight) {
    color: #ff4d4f;
    font-weight: 500;
  }

  :deep(.field-required::after) {
    content: '*';
    color: #ff4d4f;
    margin-left: 4px;
  }

  :deep(.field-locked) {
    background-color: #f0f0f0;
    color: #999;
  }

  :deep(.field-empty) {
    color: #bfbfbf;
    font-style: italic;
  }

  // 表格样式
  :deep(.s-table) {
    .s-table-header {
      background-color: #fafafa;
    }

    .s-table-body {
      .s-table-row:hover {
        background-color: #f5f5f5;
      }

      .s-table-row.row-selected {
        background-color: #e6f7ff;
      }
    }
  }
}
</style>
