<template>
  <div class="audit-item-detail">
    <!-- 使用配置化的ItemDetail组件 - 审核模块 -->
    <ConfigurableItemDetail
      module-type="shenHe"
      :table-data="tableData"
      :custom-field-config="auditFieldConfig"
      :edit-conditions="auditEditConditions"
      :project-data="projectData"
      :tab-info="tabInfo"
      :level-type="levelType"
      @data-change="handleDataChange"
      @action="handleAction"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { ConfigurableItemDetail } from '@cost-app/shared-components'

const props = defineProps({
  moduleType: String,
  levelType: Number,
  tabInfo: Object,
  projectData: Object,
  tableData: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 审核模块特有的字段配置
const auditFieldConfig = ref({
  // 基础信息在审核中都不可编辑
  bdCode: {
    editable: false,
    title: '项目编码',
    componentProps: { 
      disabled: true,
      style: { backgroundColor: '#fafafa' }
    }
  },
  name: {
    editable: false,
    title: '项目名称',
    componentProps: { 
      disabled: true,
      style: { backgroundColor: '#fafafa' }
    }
  },
  projectAttr: {
    editable: false,
    title: '项目特征',
    componentProps: { 
      disabled: true,
      style: { backgroundColor: '#fafafa' }
    }
  },
  
  // 申报数据不可编辑，仅供参考
  quantity: {
    editable: false,
    title: '申报工程量',
    component: 'number',
    componentProps: { 
      precision: 3, 
      disabled: true,
      style: { backgroundColor: '#fff2e8' }
    },
    formatter: (value) => `${Number(value || 0).toFixed(3)}`
  },
  settlementUnitPrice: {
    editable: false,
    title: '申报单价',
    component: 'number',
    componentProps: { 
      precision: 2, 
      disabled: true,
      style: { backgroundColor: '#fff2e8' }
    },
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  },
  settlementAmount: {
    editable: false,
    title: '申报合价',
    component: 'number',
    componentProps: { 
      precision: 2, 
      disabled: true,
      style: { backgroundColor: '#fff2e8' }
    },
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  },
  
  // 审核数据可编辑
  auditQuantity: {
    title: '审核工程量',
    component: 'number',
    componentProps: { 
      precision: 3, 
      min: 0,
      placeholder: '请输入审核工程量'
    },
    required: true
  },
  auditUnitPrice: {
    title: '审核单价',
    component: 'number',
    componentProps: { 
      precision: 2, 
      min: 0,
      placeholder: '请输入审核单价'
    },
    required: true
  },
  
  // 审核合价自动计算
  auditAmount: {
    editable: false,
    title: '审核合价',
    component: 'number',
    componentProps: { 
      precision: 2, 
      disabled: true,
      style: { backgroundColor: '#e6fffb' }
    },
    formatter: (value) => `¥${Number(value || 0).toFixed(2)}`
  },
  
  // 审核意见
  auditOpinion: {
    title: '审核意见',
    component: 'textarea',
    componentProps: {
      rows: 2,
      maxLength: 200,
      placeholder: '请输入审核意见...',
      showCount: true
    }
  },
  
  // 审核状态
  auditStatus: {
    title: '审核状态',
    component: 'select',
    componentProps: {
      placeholder: '请选择审核状态'
    },
    options: [
      { value: 'pending', label: '待审核', color: '#faad14' },
      { value: 'approved', label: '通过', color: '#52c41a' },
      { value: 'rejected', label: '不通过', color: '#ff4d4f' },
      { value: 'modified', label: '调整', color: '#1890ff' }
    ],
    required: true
  }
})

// 审核模块的编辑条件
const auditEditConditions = ref({
  // 审核工程量编辑条件：根据审核权限和项目状态
  auditQuantity: (context) => {
    const { record } = context
    // 已完成审核的项目不可再编辑
    if (record.auditStatus === 'approved' && record.isFinalized) {
      return false
    }
    // 检查审核权限
    return record.allowQuantityAudit !== false
  },
  
  // 审核单价编辑条件
  auditUnitPrice: (context) => {
    const { record } = context
    // 已完成审核的项目不可再编辑
    if (record.auditStatus === 'approved' && record.isFinalized) {
      return false
    }
    // 检查价格审核权限
    return record.allowPriceAudit !== false
  },
  
  // 审核意见编辑条件
  auditOpinion: (context) => {
    const { record } = context
    // 审核意见在项目完成前都可以编辑
    return record.isFinalized !== true
  },
  
  // 审核状态编辑条件
  auditStatus: (context) => {
    const { record } = context
    // 只有有审核权限的用户可以修改状态
    return record.allowStatusChange !== false && record.isFinalized !== true
  }
})

// 事件处理
const handleDataChange = (newData) => {
  emit('dataChange', newData)
}

const handleAction = (actionType, actionData) => {
  console.log('审核模块操作:', actionType, actionData)
  
  switch (actionType) {
    case 'cellEdited':
      handleCellEdited(actionData)
      break
    case 'addRow':
      message.warning('审核阶段不允许新增项目')
      break
    case 'deleteRow':
      message.warning('审核阶段不允许删除项目')
      break
    case 'contextMenu':
      handleContextMenu(actionData)
      break
    default:
      // 其他操作直接传递给父组件
      emit('action', actionType, actionData)
  }
}

const handleCellEdited = ({ record, column, newValue, oldValue }) => {
  // 审核模块特有的计算逻辑
  if (column.field === 'auditQuantity' || column.field === 'auditUnitPrice') {
    // 自动计算审核合价
    record.auditAmount = (record.auditQuantity || 0) * (record.auditUnitPrice || 0)
    
    // 计算审核差异
    const quantityDiff = (record.auditQuantity || 0) - (record.quantity || 0)
    const priceDiff = (record.auditUnitPrice || 0) - (record.settlementUnitPrice || 0)
    const amountDiff = (record.auditAmount || 0) - (record.settlementAmount || 0)
    
    record.quantityDiff = quantityDiff
    record.priceDiff = priceDiff
    record.amountDiff = amountDiff
    
    // 根据差异程度自动设置审核状态建议
    if (Math.abs(amountDiff) < 100) {
      record.suggestedStatus = 'approved'
    } else if (Math.abs(amountDiff) > 10000) {
      record.suggestedStatus = 'rejected'
    } else {
      record.suggestedStatus = 'modified'
    }
  }
  
  // 审核状态变更时的处理
  if (column.field === 'auditStatus') {
    const statusLabels = {
      'pending': '待审核',
      'approved': '通过',
      'rejected': '不通过',
      'modified': '调整'
    }
    
    // 如果是拒绝，要求必须填写审核意见
    if (newValue === 'rejected' && !record.auditOpinion) {
      message.warning('拒绝项目时必须填写审核意见')
      record.auditStatus = oldValue // 回滚状态
      return
    }
    
    message.success(`审核状态已更新为: ${statusLabels[newValue]}`)
    
    // 记录审核时间
    record.auditTime = new Date().toISOString()
  }
  
  // 审核意见变更时的处理
  if (column.field === 'auditOpinion') {
    record.hasAuditOpinion = !!newValue
    message.success('审核意见已更新')
  }
  
  message.success('审核数据更新成功')
}

const handleContextMenu = ({ key, record }) => {
  switch (key) {
    case 'viewDetail':
      message.info(`查看审核详情: ${record.name}`)
      break
    case 'viewHistory':
      message.info(`查看审核历史: ${record.name}`)
      break
    case 'exportItem':
      message.success(`导出审核项目: ${record.name}`)
      break
    case 'approveItem':
      record.auditStatus = 'approved'
      record.auditTime = new Date().toISOString()
      message.success(`批准项目: ${record.name}`)
      break
    case 'rejectItem':
      if (!record.auditOpinion) {
        message.warning('拒绝项目时必须填写审核意见')
        return
      }
      record.auditStatus = 'rejected'
      record.auditTime = new Date().toISOString()
      message.success(`拒绝项目: ${record.name}`)
      break
    case 'resetAudit':
      record.auditStatus = 'pending'
      record.auditTime = null
      message.success(`重置审核状态: ${record.name}`)
      break
    default:
      console.log('未处理的审核右键菜单操作:', key, record)
  }
}
</script>

<style lang="scss" scoped>
.audit-item-detail {
  height: 100%;
  overflow: hidden;
  
  // 审核模块特有样式
  :deep(.ant-table-tbody > tr > td) {
    // 待审核项目
    &.audit-pending {
      background-color: #fffbe6;
    }
    
    // 已通过项目
    &.audit-approved {
      background-color: #f6ffed;
    }
    
    // 已拒绝项目
    &.audit-rejected {
      background-color: #fff2f0;
    }
    
    // 已调整项目
    &.audit-modified {
      background-color: #e6f7ff;
    }
    
    // 有差异的项目高亮
    &.has-difference {
      border-left: 3px solid #faad14;
    }
    
    // 重大差异项目
    &.major-difference {
      border-left: 3px solid #ff4d4f;
    }
  }
  
  // 审核意见输入框样式
  :deep(.ant-input) {
    &.audit-opinion {
      border-color: #1890ff;
      
      &:focus {
        border-color: #40a9ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
}
</style>
