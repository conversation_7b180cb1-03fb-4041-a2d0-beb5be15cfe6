/**
 * API配置文件
 * 定义不同模块的API接口映射
 */

// 模拟API接口 - 实际使用时需要替换为真实的API导入
const createMockApi = (moduleName) => ({
  getBasicInfo: async (params) => {
    console.log(`${moduleName} - 获取基本信息:`, params)
    return {
      status: 200,
      result: [
        {
          sequenceNbr: '1',
          name: '工程名称',
          remark: '示例项目',
          type: 'normal',
          groupCode: 1,
          addFlag: 0,
          lockFlag: 0
        },
        {
          sequenceNbr: '2',
          name: '建筑面积',
          remark: '1000.00',
          type: 'normal',
          groupCode: 1,
          addFlag: 0,
          lockFlag: 0
        }
      ]
    }
  },
  
  getFeatureInfo: async (params) => {
    console.log(`${moduleName} - 获取工程特征:`, params)
    return {
      status: 200,
      result: [
        {
          sequenceNbr: '1',
          name: '结构类型',
          remark: '框架结构',
          addFlag: 0,
          lockFlag: 0
        }
      ]
    }
  },
  
  saveOrUpdate: async (params) => {
    console.log(`${moduleName} - 保存更新:`, params)
    return {
      status: 200,
      result: { success: true }
    }
  },
  
  delete: async (params) => {
    console.log(`${moduleName} - 删除:`, params)
    return {
      status: 200,
      result: { success: true }
    }
  },
  
  lock: async (params) => {
    console.log(`${moduleName} - 锁定操作:`, params)
    return {
      status: 200,
      result: { success: true }
    }
  },
  
  export: async (params) => {
    console.log(`${moduleName} - 导出:`, params)
    return {
      status: 200,
      result: { downloadUrl: 'mock-download-url' }
    }
  }
})

// API配置映射
export const apiConfigs = {
  // 概算模块API
  estimate: {
    // 实际使用时替换为真实API
    // import gsApi from '@/api/gsApi'
    ...createMockApi('概算'),
    
    // 概算特有的API配置
    getBasicInfo: async (params) => {
      // 概算模块的特殊参数处理
      const requestParams = {
        ...params,
        moduleType: 'estimate',
        // 概算特有参数
        includeSubItems: true
      }
      
      // 实际调用: return await gsApi.getBasicInfo(requestParams)
      return createMockApi('概算').getBasicInfo(requestParams)
    }
  },
  
  // 预算模块API
  budget: {
    // 实际使用时替换为真实API
    // import csProject from '@/api/csProject'
    ...createMockApi('预算'),
    
    getBasicInfo: async (params) => {
      const requestParams = {
        ...params,
        moduleType: 'budget'
      }
      
      // 实际调用: return await csProject.getBasicInfo(requestParams)
      return createMockApi('预算').getBasicInfo(requestParams)
    }
  },
  
  // 结算模块API
  settlement: {
    // 实际使用时替换为真实API
    // import jsApi from '@/api/jsApi'
    ...createMockApi('结算'),
    
    getBasicInfo: async (params) => {
      const requestParams = {
        ...params,
        moduleType: 'settlement',
        // 结算特有参数
        includeContract: true,
        includeChanges: true
      }
      
      // 实际调用: return await jsApi.getBasicInfoJieSuan(requestParams)
      return createMockApi('结算').getBasicInfo(requestParams)
    },
    
    saveOrUpdate: async (params) => {
      // 结算模块使用特殊的保存接口
      const requestParams = {
        ...params,
        settlementType: 'basic-info'
      }
      
      // 实际调用: return await jsApi.jsSaveBasicEngineeringInfoOrEngineeringFeature(requestParams)
      return createMockApi('结算').saveOrUpdate(requestParams)
    }
  },
  
  // 审核模块API
  review: {
    // 实际使用时替换为真实API
    // import shApi from '@/api/shApi'
    ...createMockApi('审核'),
    
    getBasicInfo: async (params) => {
      const requestParams = {
        ...params,
        moduleType: 'review',
        // 审核特有参数
        includeAuditTrail: true
      }
      
      // 实际调用: return await shApi.getBasicInfo(requestParams)
      return createMockApi('审核').getBasicInfo(requestParams)
    }
  },
  
  // 工料机模块API
  material: {
    // 实际使用时替换为真实API
    // import gljApi from '@/api/gljApi'
    ...createMockApi('工料机'),
    
    getBasicInfo: async (params) => {
      const requestParams = {
        ...params,
        moduleType: 'material',
        // 工料机特有参数
        includeLabor: true,
        includeMaterial: true,
        includeMachine: true
      }
      
      // 实际调用: return await gljApi.getBasicInfo(requestParams)
      return createMockApi('工料机').getBasicInfo(requestParams)
    }
  }
}

/**
 * 获取API配置
 * @param {String} moduleType 模块类型
 * @returns {Object} API配置对象
 */
export function getApiConfig(moduleType) {
  const config = apiConfigs[moduleType]
  
  if (!config) {
    console.warn(`未找到模块 ${moduleType} 的API配置，使用默认配置`)
    return apiConfigs.budget // 默认使用预算模块的API
  }
  
  return config
}

/**
 * 创建统一的错误处理包装器
 * @param {Function} apiFunction API函数
 * @param {String} operationName 操作名称
 * @returns {Function} 包装后的API函数
 */
export function createApiWrapper(apiFunction, operationName) {
  return async (...args) => {
    try {
      const result = await apiFunction(...args)
      
      // 统一的响应格式检查
      if (result && result.status === 200) {
        return result
      } else {
        throw new Error(result?.message || `${operationName}失败`)
      }
    } catch (error) {
      console.error(`${operationName}错误:`, error)
      
      // 统一的错误格式
      throw {
        message: error.message || `${operationName}失败`,
        code: error.code || 'API_ERROR',
        details: error
      }
    }
  }
}

/**
 * 为模块API添加错误处理包装
 * @param {String} moduleType 模块类型
 * @returns {Object} 包装后的API配置
 */
export function getWrappedApiConfig(moduleType) {
  const config = getApiConfig(moduleType)
  const wrappedConfig = {}
  
  // 为每个API方法添加错误处理包装
  Object.keys(config).forEach(key => {
    if (typeof config[key] === 'function') {
      wrappedConfig[key] = createApiWrapper(config[key], `${moduleType}-${key}`)
    } else {
      wrappedConfig[key] = config[key]
    }
  })
  
  return wrappedConfig
}

/**
 * 批量API调用工具
 * @param {Array} apiCalls API调用配置数组
 * @returns {Promise<Array>} 批量调用结果
 */
export async function batchApiCall(apiCalls) {
  const promises = apiCalls.map(async (call) => {
    try {
      const { api, params, key } = call
      const result = await api(params)
      return { key, success: true, data: result }
    } catch (error) {
      return { key: call.key, success: false, error }
    }
  })
  
  return await Promise.all(promises)
}

/**
 * API缓存管理器
 */
class ApiCache {
  constructor() {
    this.cache = new Map()
    this.ttl = 5 * 60 * 1000 // 5分钟缓存
  }
  
  generateKey(moduleType, method, params) {
    return `${moduleType}-${method}-${JSON.stringify(params)}`
  }
  
  get(moduleType, method, params) {
    const key = this.generateKey(moduleType, method, params)
    const cached = this.cache.get(key)
    
    if (cached && Date.now() - cached.timestamp < this.ttl) {
      return cached.data
    }
    
    return null
  }
  
  set(moduleType, method, params, data) {
    const key = this.generateKey(moduleType, method, params)
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    })
  }
  
  clear(moduleType = null) {
    if (moduleType) {
      // 清除特定模块的缓存
      for (const [key] of this.cache) {
        if (key.startsWith(moduleType)) {
          this.cache.delete(key)
        }
      }
    } else {
      // 清除所有缓存
      this.cache.clear()
    }
  }
}

// 导出缓存实例
export const apiCache = new ApiCache()

/**
 * 带缓存的API调用
 * @param {String} moduleType 模块类型
 * @param {String} method 方法名
 * @param {Object} params 参数
 * @param {Boolean} useCache 是否使用缓存
 * @returns {Promise} API调用结果
 */
export async function cachedApiCall(moduleType, method, params, useCache = true) {
  // 检查缓存
  if (useCache) {
    const cached = apiCache.get(moduleType, method, params)
    if (cached) {
      return cached
    }
  }
  
  // 调用API
  const config = getApiConfig(moduleType)
  const result = await config[method](params)
  
  // 缓存结果
  if (useCache && result.status === 200) {
    apiCache.set(moduleType, method, params, result)
  }
  
  return result
}
