/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ABreadcrumb: typeof import('ant-design-vue/es')['Breadcrumb']
    ABreadcrumbItem: typeof import('ant-design-vue/es')['BreadcrumbItem']
    AButton: typeof import('ant-design-vue/es')['Button']
    ACard: typeof import('ant-design-vue/es')['Card']
    ACol: typeof import('ant-design-vue/es')['Col']
    ADatePicker: typeof import('ant-design-vue/es')['DatePicker']
    ADivider: typeof import('ant-design-vue/es')['Divider']
    AEmpty: typeof import('ant-design-vue/es')['Empty']
    AForm: typeof import('ant-design-vue/es')['Form']
    AFormItem: typeof import('ant-design-vue/es')['FormItem']
    AInput: typeof import('ant-design-vue/es')['Input']
    AInputNumber: typeof import('ant-design-vue/es')['InputNumber']
    AMenu: typeof import('ant-design-vue/es')['Menu']
    AMenuDivider: typeof import('ant-design-vue/es')['MenuDivider']
    AMenuItem: typeof import('ant-design-vue/es')['MenuItem']
    AProgress: typeof import('ant-design-vue/es')['Progress']
    AResult: typeof import('ant-design-vue/es')['Result']
    ARow: typeof import('ant-design-vue/es')['Row']
    ASelect: typeof import('ant-design-vue/es')['Select']
    ASelectOption: typeof import('ant-design-vue/es')['SelectOption']
    ASkeleton: typeof import('ant-design-vue/es')['Skeleton']
    ASpace: typeof import('ant-design-vue/es')['Space']
    AStatistic: typeof import('ant-design-vue/es')['Statistic']
    ASubMenu: typeof import('ant-design-vue/es')['SubMenu']
    ATag: typeof import('ant-design-vue/es')['Tag']
    ATooltip: typeof import('ant-design-vue/es')['Tooltip']
    ATypographyText: typeof import('ant-design-vue/es')['TypographyText']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
