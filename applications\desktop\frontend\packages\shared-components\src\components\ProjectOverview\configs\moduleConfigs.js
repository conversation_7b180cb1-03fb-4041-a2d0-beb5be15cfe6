/**
 * 模块配置文件
 * 定义不同业务模块的配置信息
 */

// 通用字段配置
export const commonFields = {
  projectName: {
    name: '工程名称',
    type: 'text',
    required: true,
    editable: true,
    maxLength: 100,
    highlight: false
  },
  projectCode: {
    name: '项目编号',
    type: 'text',
    required: true,
    editable: true,
    maxLength: 50,
    highlight: false
  },
  buildingArea: {
    name: '建筑面积',
    type: 'number',
    required: true,
    editable: true,
    precision: 2,
    suffix: '㎡',
    highlight: true
  },
  projectScale: {
    name: '工程规模',
    type: 'number',
    required: false,
    editable: true,
    precision: 2,
    suffix: '万元',
    highlight: true
  },
  constructionUnit: {
    name: '建设单位',
    type: 'text',
    required: false,
    editable: true,
    maxLength: 100,
    highlight: false
  },
  designUnit: {
    name: '设计单位',
    type: 'text',
    required: false,
    editable: true,
    maxLength: 100,
    highlight: false
  },
  compileDate: {
    name: '编制时间',
    type: 'date',
    required: true,
    editable: true,
    format: 'YYYY-MM-DD',
    highlight: true
  },
  compiler: {
    name: '编制人',
    type: 'text',
    required: true,
    editable: true,
    maxLength: 50,
    highlight: true
  },
  reviewer: {
    name: '核对人(复核人)',
    type: 'text',
    required: false,
    editable: true,
    maxLength: 50,
    highlight: true
  },
  reviewDate: {
    name: '核对(复核)时间',
    type: 'date',
    required: false,
    editable: true,
    format: 'YYYY-MM-DD',
    highlight: true
  }
}

// 模块特定配置
export const moduleConfigs = {
  // 概算模块
  estimate: {
    name: '概算',
    code: 'estimate',
    color: '#1890ff',
    icon: 'CalculatorOutlined',
    description: '工程概算编制',
    
    // 特有字段
    fields: {
      ...commonFields,
      estimateAccuracy: {
        name: '估算精度',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: 'A级', value: 'A' },
          { label: 'B级', value: 'B' },
          { label: 'C级', value: 'C' }
        ],
        highlight: false
      },
      estimateStandard: {
        name: '概算标准',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: '国家标准', value: 'national' },
          { label: '行业标准', value: 'industry' },
          { label: '地方标准', value: 'local' }
        ],
        highlight: false
      }
    },
    
    // 权限配置
    permissions: {
      canEdit: true,
      canDelete: true,
      canLock: true,
      canExport: true,
      restrictedFields: [] // 限制编辑的字段
    },
    
    // 显示配置
    display: {
      showEngineerFeature: true,
      showLockButton: true,
      showExportButton: true
    }
  },
  
  // 预算模块
  budget: {
    name: '预算',
    code: 'budget',
    color: '#52c41a',
    icon: 'FileTextOutlined',
    description: '工程预算编制',
    
    fields: {
      ...commonFields,
      quotaStandard: {
        name: '定额标准',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: '国家标准', value: 'national' },
          { label: '地方标准', value: 'local' },
          { label: '企业标准', value: 'enterprise' }
        ],
        highlight: false
      },
      pricingMode: {
        name: '计价模式',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: '工程量清单计价', value: 'list' },
          { label: '定额计价', value: 'quota' },
          { label: '混合计价', value: 'mixed' }
        ],
        highlight: false
      },
      budgetType: {
        name: '预算类型',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: '施工图预算', value: 'construction' },
          { label: '招标控制价', value: 'tender' },
          { label: '投标报价', value: 'bid' }
        ],
        highlight: false
      }
    },
    
    permissions: {
      canEdit: true,
      canDelete: true,
      canLock: true,
      canExport: true,
      restrictedFields: []
    },
    
    display: {
      showEngineerFeature: true,
      showLockButton: true,
      showExportButton: true
    }
  },
  
  // 结算模块
  settlement: {
    name: '结算',
    code: 'settlement',
    color: '#fa8c16',
    icon: 'AuditOutlined',
    description: '工程结算编制',
    
    fields: {
      ...commonFields,
      settlementType: {
        name: '结算类型',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: '竣工结算', value: 'final' },
          { label: '阶段结算', value: 'stage' },
          { label: '专项结算', value: 'special' }
        ],
        highlight: false
      },
      contractAmount: {
        name: '合同金额',
        type: 'number',
        required: false,
        editable: true,
        precision: 2,
        suffix: '万元',
        highlight: false
      },
      actualAmount: {
        name: '实际金额',
        type: 'number',
        required: false,
        editable: true,
        precision: 2,
        suffix: '万元',
        highlight: false
      }
    },
    
    permissions: {
      canEdit: true,
      canDelete: true,
      canLock: true,
      canExport: true,
      restrictedFields: ['projectName', 'projectCode'] // 结算中项目名称和编码不可编辑
    },
    
    display: {
      showEngineerFeature: true,
      showLockButton: true,
      showExportButton: true
    }
  },
  
  // 审核模块
  review: {
    name: '审核',
    code: 'review',
    color: '#722ed1',
    icon: 'CheckCircleOutlined',
    description: '工程审核',
    
    fields: {
      ...commonFields,
      reviewType: {
        name: '审核类型',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: '预算审核', value: 'budget' },
          { label: '结算审核', value: 'settlement' },
          { label: '变更审核', value: 'change' }
        ],
        highlight: false
      },
      auditOpinion: {
        name: '审核意见',
        type: 'textarea',
        required: false,
        editable: true,
        maxLength: 500,
        rows: 3,
        highlight: false
      },
      auditResult: {
        name: '审核结果',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: '通过', value: 'approved' },
          { label: '不通过', value: 'rejected' },
          { label: '需修改', value: 'modified' }
        ],
        highlight: true
      }
    },
    
    permissions: {
      canEdit: true,
      canDelete: false, // 审核记录不可删除
      canLock: true,
      canExport: true,
      restrictedFields: ['projectName', 'projectCode', 'buildingArea'] // 审核中基础信息不可编辑
    },
    
    display: {
      showEngineerFeature: false, // 审核模块不显示工程特征
      showLockButton: true,
      showExportButton: true
    }
  },
  
  // 工料机模块
  material: {
    name: '工料机',
    code: 'material',
    color: '#13c2c2',
    icon: 'ToolOutlined',
    description: '人工材料机械',
    
    fields: {
      ...commonFields,
      laborStandard: {
        name: '人工标准',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: '当地标准', value: 'local' },
          { label: '行业标准', value: 'industry' },
          { label: '企业标准', value: 'enterprise' }
        ],
        highlight: false
      },
      materialStandard: {
        name: '材料标准',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: '市场价', value: 'market' },
          { label: '信息价', value: 'info' },
          { label: '询价', value: 'inquiry' }
        ],
        highlight: false
      },
      machineStandard: {
        name: '机械标准',
        type: 'select',
        required: true,
        editable: true,
        options: [
          { label: '台班单价', value: 'shift' },
          { label: '租赁价格', value: 'rental' },
          { label: '折旧价格', value: 'depreciation' }
        ],
        highlight: false
      }
    },
    
    permissions: {
      canEdit: true,
      canDelete: true,
      canLock: true,
      canExport: true,
      restrictedFields: []
    },
    
    display: {
      showEngineerFeature: false, // 工料机模块不显示工程特征
      showLockButton: true,
      showExportButton: true
    }
  }
}

/**
 * 获取模块配置
 * @param {String} moduleType 模块类型
 * @returns {Object} 模块配置
 */
export function getModuleConfig(moduleType) {
  return moduleConfigs[moduleType] || moduleConfigs.budget
}

/**
 * 获取模块字段配置
 * @param {String} moduleType 模块类型
 * @returns {Object} 字段配置
 */
export function getModuleFieldConfig(moduleType) {
  const config = getModuleConfig(moduleType)
  return config.fields || {}
}

/**
 * 获取模块权限配置
 * @param {String} moduleType 模块类型
 * @returns {Object} 权限配置
 */
export function getModulePermissions(moduleType) {
  const config = getModuleConfig(moduleType)
  return config.permissions || {}
}

/**
 * 获取模块显示配置
 * @param {String} moduleType 模块类型
 * @returns {Object} 显示配置
 */
export function getModuleDisplay(moduleType) {
  const config = getModuleConfig(moduleType)
  return config.display || {}
}

/**
 * 检查字段是否受限制
 * @param {String} moduleType 模块类型
 * @param {String} fieldName 字段名称
 * @returns {Boolean} 是否受限制
 */
export function isFieldRestricted(moduleType, fieldName) {
  const permissions = getModulePermissions(moduleType)
  return permissions.restrictedFields?.includes(fieldName) || false
}

/**
 * 获取所有模块列表
 * @returns {Array} 模块列表
 */
export function getAllModules() {
  return Object.keys(moduleConfigs).map(key => ({
    key,
    ...moduleConfigs[key]
  }))
}
