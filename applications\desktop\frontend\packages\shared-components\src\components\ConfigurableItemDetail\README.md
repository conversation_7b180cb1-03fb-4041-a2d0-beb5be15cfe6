# ConfigurableItemDetail 配置化详情组件使用指南

## 🎯 概述

`ConfigurableItemDetail` 是一个高度配置化的造价详情组件，专门为概算、预算、结算、审核等不同业务模块设计。通过配置驱动的方式，实现了同一组件在不同业务场景下的差异化表现。

## ✨ 核心特性

- 🔧 **配置驱动**: 通过字段配置和编辑条件控制组件行为
- 🎯 **模块化**: 支持概算(gaiSuan)、预算(yuSuan)、结算(jieSuan)、审核(shenHe)四个模块
- 📝 **动态编辑**: 根据业务规则动态控制字段的可编辑性
- 🎨 **组件多样**: 支持输入框、下拉选择、数字输入、文本域等多种组件类型
- 🔒 **权限控制**: 基于上下文的细粒度编辑权限控制

## 🚀 快速开始

### 基本使用

```vue
<template>
  <ConfigurableItemDetail
    module-type="yuSuan"
    :table-data="tableData"
    :custom-field-config="fieldConfig"
    :edit-conditions="editConditions"
    @data-change="handleDataChange"
    @action="handleAction"
  />
</template>

<script setup>
import { ref } from 'vue'
import { ConfigurableItemDetail } from '@cost-app/shared-components'

const tableData = ref([])
const fieldConfig = ref({})
const editConditions = ref({})
</script>
```

## 📋 Props 配置

### 基础配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| moduleType | String | - | 模块类型：gaiSuan/yuSuan/jieSuan/shenHe |
| tableData | Array | [] | 表格数据 |
| customFieldConfig | Object | {} | 自定义字段配置 |
| editConditions | Object | {} | 编辑条件配置 |
| projectData | Object | {} | 项目数据 |
| tabInfo | Object | {} | 标签信息 |

### 高级配置

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| customColumns | Array | [] | 自定义列配置 |
| editable | Boolean | true | 是否可编辑 |
| showToolbar | Boolean | true | 是否显示工具栏 |
| showSummary | Boolean | true | 是否显示汇总 |
| scrollConfig | Object | {...} | 滚动配置 |

## 🔧 字段配置详解

### 字段配置结构

```javascript
const fieldConfig = {
  fieldName: {
    title: '字段标题',
    component: 'input', // 组件类型
    componentProps: {}, // 组件属性
    editable: true, // 是否可编辑
    required: false, // 是否必填
    formatter: (value) => value, // 格式化函数
    options: [], // 选项数据（select组件）
    validator: (value) => true // 验证函数
  }
}
```

### 支持的组件类型

#### 1. 输入框 (input)
```javascript
{
  component: 'input',
  componentProps: {
    placeholder: '请输入...',
    maxLength: 100
  }
}
```

#### 2. 数字输入 (number)
```javascript
{
  component: 'number',
  componentProps: {
    precision: 2,
    min: 0,
    max: 999999,
    placeholder: '请输入数字'
  }
}
```

#### 3. 下拉选择 (select)
```javascript
{
  component: 'select',
  componentProps: {
    placeholder: '请选择...',
    showSearch: true
  },
  options: [
    { value: 'option1', label: '选项1' },
    { value: 'option2', label: '选项2' }
  ]
}
```

#### 4. 文本域 (textarea)
```javascript
{
  component: 'textarea',
  componentProps: {
    rows: 3,
    maxLength: 200,
    showCount: true
  }
}
```

#### 5. 复选框 (checkbox)
```javascript
{
  component: 'checkbox',
  componentProps: {
    checkedChildren: '是',
    unCheckedChildren: '否'
  }
}
```

## 🎯 模块特定配置示例

### 预算模块 (yuSuan)

```javascript
const budgetFieldConfig = {
  quantity: {
    title: '工程量',
    component: 'number',
    componentProps: { precision: 3, min: 0 },
    required: true
  },
  budgetUnitPrice: {
    title: '预算单价',
    component: 'number',
    componentProps: { precision: 2, min: 0 },
    required: true
  },
  quotaCode: {
    title: '定额编号',
    component: 'select',
    componentProps: { showSearch: true },
    options: [] // 从定额库加载
  }
}

const budgetEditConditions = {
  quantity: () => true, // 预算阶段工程量都可编辑
  budgetUnitPrice: (context) => {
    return context.record.status !== 'locked'
  }
}
```

### 结算模块 (jieSuan)

```javascript
const settlementFieldConfig = {
  bdCode: {
    editable: false, // 结算中项目编码不可编辑
    componentProps: { disabled: true }
  },
  quantity: {
    title: '实际工程量',
    component: 'number',
    componentProps: { precision: 3 }
  },
  contractType: {
    title: '合同类型',
    component: 'select',
    options: [
      { value: 'within', label: '合同内' },
      { value: 'external', label: '合同外' },
      { value: 'change', label: '变更' }
    ]
  }
}

const settlementEditConditions = {
  quantity: (context) => {
    // 只有合同外和变更项目可编辑工程量
    const { record } = context
    return record.contractType === 'external' || record.contractType === 'change'
  }
}
```

### 审核模块 (shenHe)

```javascript
const auditFieldConfig = {
  quantity: {
    editable: false, // 申报工程量不可编辑
    title: '申报工程量',
    componentProps: { disabled: true }
  },
  auditQuantity: {
    title: '审核工程量',
    component: 'number',
    componentProps: { precision: 3, min: 0 },
    required: true
  },
  auditOpinion: {
    title: '审核意见',
    component: 'textarea',
    componentProps: { rows: 2, maxLength: 200 }
  },
  auditStatus: {
    title: '审核状态',
    component: 'select',
    options: [
      { value: 'approved', label: '通过' },
      { value: 'rejected', label: '不通过' },
      { value: 'modified', label: '调整' }
    ]
  }
}

const auditEditConditions = {
  auditQuantity: (context) => {
    return context.record.auditStatus !== 'approved'
  },
  auditOpinion: () => true // 审核意见总是可编辑
}
```

## 🔒 编辑条件详解

编辑条件函数接收一个上下文对象，包含以下属性：

```javascript
const context = {
  record, // 当前行数据
  column, // 当前列配置
  moduleType // 模块类型
}
```

### 常见编辑条件模式

#### 1. 基于状态的编辑控制
```javascript
const editConditions = {
  fieldName: (context) => {
    return context.record.status !== 'locked'
  }
}
```

#### 2. 基于权限的编辑控制
```javascript
const editConditions = {
  fieldName: (context) => {
    return context.record.allowEdit === true
  }
}
```

#### 3. 基于业务规则的编辑控制
```javascript
const editConditions = {
  quantity: (context) => {
    // 结算模块中的特殊规则
    if (context.moduleType === 'jieSuan') {
      return context.record.contractType !== 'within'
    }
    return true
  }
}
```

## 📊 事件处理

### 数据变更事件
```javascript
const handleDataChange = (newData) => {
  console.log('数据已更新:', newData)
  // 处理数据变更逻辑
}
```

### 操作事件
```javascript
const handleAction = (actionType, actionData) => {
  switch (actionType) {
    case 'cellEdited':
      // 单元格编辑完成
      break
    case 'addRow':
      // 新增行
      break
    case 'deleteRow':
      // 删除行
      break
    case 'contextMenu':
      // 右键菜单操作
      break
  }
}
```

## 🎨 样式定制

### CSS 变量
```css
:root {
  --configurable-item-detail-bg: #ffffff;
  --configurable-item-detail-border: #d9d9d9;
  --configurable-item-detail-hover: #f5f5f5;
}
```

### 模块特定样式
```scss
.configurable-item-detail {
  // 预算模块样式
  &.module-yuSuan {
    --primary-color: #52c41a;
  }
  
  // 结算模块样式
  &.module-jieSuan {
    --primary-color: #722ed1;
  }
  
  // 审核模块样式
  &.module-shenHe {
    --primary-color: #eb2f96;
  }
}
```

## 🔍 最佳实践

### 1. 配置分离
将字段配置和编辑条件分离到独立的文件中：

```javascript
// configs/budgetConfig.js
export const budgetFieldConfig = { /* ... */ }
export const budgetEditConditions = { /* ... */ }
```

### 2. 动态配置加载
根据用户权限动态加载配置：

```javascript
const loadFieldConfig = async (moduleType, userPermissions) => {
  const baseConfig = await import(`./configs/${moduleType}Config.js`)
  return applyPermissions(baseConfig, userPermissions)
}
```

### 3. 配置验证
在开发环境中验证配置的正确性：

```javascript
const validateConfig = (config) => {
  // 验证配置结构
  // 检查必需字段
  // 验证组件类型
}
```

## 🐛 常见问题

### Q: 如何添加新的组件类型？
A: 在 `ConfigurableItemDetail.vue` 中的 `enhanceColumnsWithEditConfig` 方法中添加新的组件类型处理逻辑。

### Q: 编辑条件不生效怎么办？
A: 检查编辑条件函数是否正确返回布尔值，确保上下文对象包含所需的数据。

### Q: 如何实现字段间的联动？
A: 在 `performModuleSpecificCalculation` 方法中添加字段联动逻辑。

## 📚 相关文档

- [CostTable 组件文档](../CostTable/README.md)
- [模块配置文档](../CostTable/moduleConfigs.js)
- [架构设计文档](../../AGENT_ARCHITECTURE_GUIDE.md)
