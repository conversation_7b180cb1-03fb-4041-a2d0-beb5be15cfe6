<template>
  <div class="material-project-overview">
    <!-- 使用新的ProjectOverview共享组件 -->
    <ProjectOverview
      module-type="material"
      :level-type="levelType"
      :project-data="projectData"
      :tab-info="tabInfo"
      :editable="editable"
      :custom-config="customConfig"
      :permissions="permissions"
      @data-change="handleDataChange"
      @save="handleSave"
      @export="handleExport"
      @lock="handleLock"
      @action="handleAction"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { ProjectOverview } from '@cost-app/shared-components'

const props = defineProps({
  levelType: {
    type: Number,
    default: 3
  },
  tabInfo: {
    type: Object,
    default: () => ({ code: 'project-overview', name: '项目概况' })
  },
  projectId: {
    type: String,
    default: ''
  },
  unitId: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['dataChange', 'action'])

// 响应式数据
const editable = ref(true)
const projectData = ref({
  projectName: '',
  projectCode: '',
  buildingArea: null,
  projectScale: null,
  constructionUnit: '',
  designUnit: '',
  compileDate: '',
  compiler: '',
  reviewer: '',
  reviewDate: '',
  // 工料机特有字段
  laborStandard: '',
  materialStandard: '',
  machineStandard: ''
})

// 自定义配置
const customConfig = computed(() => ({
  // 工料机模块特定配置
  hiddenFields: [], // 隐藏的字段
  hideEngineerFeature: true, // 工料机模块不显示工程特征
  validationRules: {
    projectName: [
      { required: true, message: '项目名称不能为空' }
    ],
    laborStandard: [
      { required: true, message: '人工标准不能为空' }
    ],
    materialStandard: [
      { required: true, message: '材料标准不能为空' }
    ],
    machineStandard: [
      { required: true, message: '机械标准不能为空' }
    ]
  },
  // 工料机特有字段配置
  extraFields: {
    laborStandard: {
      name: '人工标准',
      type: 'select',
      required: true,
      options: [
        { label: '当地标准', value: 'local' },
        { label: '行业标准', value: 'industry' },
        { label: '企业标准', value: 'enterprise' }
      ]
    },
    materialStandard: {
      name: '材料标准',
      type: 'select',
      required: true,
      options: [
        { label: '市场价', value: 'market' },
        { label: '信息价', value: 'info' },
        { label: '询价', value: 'inquiry' }
      ]
    },
    machineStandard: {
      name: '机械标准',
      type: 'select',
      required: true,
      options: [
        { label: '台班单价', value: 'shift' },
        { label: '租赁价格', value: 'rental' },
        { label: '折旧价格', value: 'depreciation' }
      ]
    }
  }
}))

// 权限配置
const permissions = computed(() => ({
  canEdit: true,
  canDelete: true,
  canLock: true,
  canExport: true,
  canImport: false
}))

// 事件处理
const handleDataChange = (data) => {
  console.log('工料机概况数据变更:', data)
  
  // 更新本地数据
  if (data.type === 'basic') {
    updateProjectData(data.data)
  }
  
  emit('dataChange', data)
}

const handleSave = (data) => {
  console.log('保存工料机概况:', data)
  
  // 工料机模块特有的保存逻辑
  const saveData = {
    ...data,
    moduleType: 'material',
    saveTime: new Date().toISOString()
  }
  
  // 调用工料机API保存
  saveMaterialOverview(saveData)
  
  emit('action', 'save', saveData)
}

const handleExport = (data) => {
  console.log('导出工料机概况:', data)
  
  // 工料机模块特有的导出逻辑
  const exportData = {
    ...data,
    exportType: 'material-overview',
    exportTime: new Date().toISOString()
  }
  
  // 调用工料机API导出
  exportMaterialOverview(exportData)
  
  emit('action', 'export', exportData)
}

const handleLock = (lockStatus) => {
  console.log('锁定状态变更:', lockStatus)
  
  // 工料机模块特有的锁定逻辑
  updateLockStatus(lockStatus)
  
  emit('action', 'lock', lockStatus)
}

const handleAction = (action, data) => {
  console.log('工料机概况操作:', action, data)
  emit('action', action, data)
}

// 业务逻辑函数
const updateProjectData = (data) => {
  Object.assign(projectData.value, data)
}

const saveMaterialOverview = async (data) => {
  try {
    // 调用工料机API保存概况信息
    console.log('保存工料机概况成功:', data)
    message.success('工料机概况保存成功')
  } catch (error) {
    console.error('保存工料机概况失败:', error)
    message.error('保存失败')
  }
}

const exportMaterialOverview = async (data) => {
  try {
    // 调用工料机API导出概况信息
    console.log('导出工料机概况成功:', data)
    message.success('工料机概况导出成功')
  } catch (error) {
    console.error('导出工料机概况失败:', error)
    message.error('导出失败')
  }
}

const updateLockStatus = async (lockStatus) => {
  try {
    // 调用工料机API更新锁定状态
    console.log('更新锁定状态成功:', lockStatus)
    message.success(lockStatus ? '锁定成功' : '解锁成功')
  } catch (error) {
    console.error('更新锁定状态失败:', error)
    message.error('操作失败')
  }
}

// 数据加载函数
const loadProjectData = async () => {
  if (!props.projectId) return

  try {
    // 加载项目基本数据
    // const data = await materialApi.getProjectOverview(props.projectId)
    // Object.assign(projectData.value, data)
    console.log('加载项目数据:', props.projectId)
  } catch (error) {
    console.error('加载项目数据失败:', error)
    message.error('加载数据失败')
  }
}

// 监听器
watch(() => props.projectId, () => {
  // 项目ID变化时重新加载数据
  loadProjectData()
}, { immediate: true })

// 生命周期
onMounted(() => {
  console.log('工料机概况组件已挂载')
  loadProjectData()
})
</script>

<style lang="scss" scoped>
.material-project-overview {
  height: 100%;
  
  // 工料机模块特有样式
  :deep(.project-overview) {
    .page-header h3 {
      color: #13c2c2; // 工料机模块主色调
    }
    
    .ant-tabs-tab {
      &.ant-tabs-tab-active {
        color: #13c2c2;
      }
    }
    
    .field-highlight {
      color: #13c2c2;
    }
  }
}
</style>
