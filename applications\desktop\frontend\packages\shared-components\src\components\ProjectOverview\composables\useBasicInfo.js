import { ref, reactive, computed } from 'vue'
import { message } from 'ant-design-vue'

/**
 * 基本信息业务逻辑组合式API
 */
export function useBasicInfo() {
  // 状态管理
  const selectedRecord = ref(null)
  const clipboard = reactive({
    data: null,
    type: null
  })
  
  // 新增记录
  const addNewRecord = (parentRecord = null) => {
    const newRecord = {
      sequenceNbr: Date.now(), // 临时ID
      name: '',
      remark: '',
      addFlag: 1, // 标记为新增
      lockFlag: 0,
      type: 'normal',
      groupCode: parentRecord?.groupCode || 1,
      parentId: parentRecord?.sequenceNbr || null,
      childrenList: []
    }
    
    return newRecord
  }
  
  // 删除记录
  const deleteRecord = (record) => {
    if (!record) {
      message.warning('请选择要删除的记录')
      return false
    }
    
    if (!record.addFlag) {
      message.warning('只能删除新增的记录')
      return false
    }
    
    return true
  }
  
  // 复制记录
  const copyRecord = (record) => {
    if (!record) {
      message.warning('请选择要复制的记录')
      return
    }
    
    clipboard.data = {
      name: record.name,
      remark: record.remark,
      type: record.type,
      groupCode: record.groupCode
    }
    clipboard.type = 'record'
    
    message.success('已复制到剪贴板')
  }
  
  // 粘贴记录
  const pasteRecord = (targetRecord = null) => {
    if (!clipboard.data || clipboard.type !== 'record') {
      message.warning('剪贴板为空')
      return null
    }
    
    const newRecord = {
      ...clipboard.data,
      sequenceNbr: Date.now(),
      addFlag: 1,
      lockFlag: 0,
      parentId: targetRecord?.sequenceNbr || null,
      childrenList: []
    }
    
    return newRecord
  }
  
  // 检查是否可以粘贴
  const canPaste = computed(() => {
    return clipboard.data && clipboard.type === 'record'
  })
  
  // 验证记录数据
  const validateRecord = (record) => {
    const errors = []
    
    if (!record.name || record.name.trim() === '') {
      errors.push('名称不能为空')
    }
    
    if (record.name && record.name.length > 100) {
      errors.push('名称长度不能超过100个字符')
    }
    
    if (record.remark && record.remark.length > 500) {
      errors.push('备注长度不能超过500个字符')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }
  
  // 格式化数值字段
  const formatNumericField = (value, precision = 2) => {
    if (!value || isNaN(value)) return ''
    return Number(value).toFixed(precision)
  }
  
  // 格式化日期字段
  const formatDateField = (value, format = 'YYYY-MM-DD') => {
    if (!value) return ''
    
    try {
      const date = new Date(value)
      if (isNaN(date.getTime())) return value
      
      // 简单的日期格式化
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      
      return `${year}-${month}-${day}`
    } catch (error) {
      return value
    }
  }
  
  // 处理字段变更
  const handleFieldChange = (record, fieldName, newValue, oldValue) => {
    // 记录变更历史
    if (!record._changes) {
      record._changes = {}
    }
    
    record._changes[fieldName] = {
      oldValue,
      newValue,
      timestamp: Date.now()
    }
    
    // 特殊字段处理
    switch (fieldName) {
      case 'buildingArea':
      case 'projectScale':
        // 建筑面积和工程规模需要格式化为数值
        if (newValue && !isNaN(newValue)) {
          record[fieldName] = formatNumericField(newValue, 2)
        }
        break
        
      case 'compileDate':
      case 'reviewDate':
        // 日期字段格式化
        record[fieldName] = formatDateField(newValue)
        break
        
      default:
        record[fieldName] = newValue
    }
    
    // 标记为已修改
    record._modified = true
  }
  
  // 获取字段显示值
  const getFieldDisplayValue = (record, fieldName) => {
    const value = record[fieldName] || record.remark
    
    if (!value) return ''
    
    // 根据字段类型格式化显示值
    switch (fieldName) {
      case 'buildingArea':
        return value ? `${value} ㎡` : ''
      case 'projectScale':
        return value ? `${value} 万元` : ''
      case 'compileDate':
      case 'reviewDate':
        return formatDateField(value)
      default:
        return value
    }
  }
  
  // 检查字段是否为必填
  const isRequiredField = (fieldName) => {
    const requiredFields = [
      '工程名称',
      '项目编号', 
      '建筑面积',
      '编制时间',
      '编制人'
    ]
    
    return requiredFields.includes(fieldName)
  }
  
  // 检查字段是否需要高亮显示
  const isHighlightField = (fieldName) => {
    const highlightFields = [
      '建筑面积',
      '工程规模',
      '编制单位法定代表人',
      '招标人(发包人)',
      '招标人(发包人)法人或其授权人',
      '工程造价咨询人',
      '工程造价咨询人法人或其授权人',
      '编制人',
      '编制时间',
      '核对人(复核人)',
      '核对(复核)时间',
      '投标人(承包人)',
      '投标人(承包人)法人或其授权人'
    ]
    
    return highlightFields.includes(fieldName)
  }
  
  // 获取字段编辑组件类型
  const getFieldEditType = (fieldName) => {
    const dateFields = ['开工日期', '竣工日期', '编制时间', '核对(复核)时间']
    const numberFields = ['建筑面积', '工程规模']
    const selectFields = ['工程类别', '结构类型', '装修标准']
    
    if (dateFields.includes(fieldName)) {
      return 'date'
    } else if (numberFields.includes(fieldName)) {
      return 'number'
    } else if (selectFields.includes(fieldName)) {
      return 'select'
    } else {
      return 'text'
    }
  }
  
  // 构建树形结构数据
  const buildTreeData = (flatData) => {
    const tree = []
    const map = new Map()
    
    // 第一遍：创建所有节点
    flatData.forEach(item => {
      map.set(item.sequenceNbr, {
        ...item,
        childrenList: []
      })
    })
    
    // 第二遍：建立父子关系
    flatData.forEach(item => {
      const node = map.get(item.sequenceNbr)
      
      if (item.parentId && map.has(item.parentId)) {
        const parent = map.get(item.parentId)
        parent.childrenList.push(node)
      } else {
        tree.push(node)
      }
    })
    
    return tree
  }
  
  // 扁平化树形数据
  const flattenTreeData = (treeData) => {
    const result = []
    
    const traverse = (nodes) => {
      nodes.forEach(node => {
        result.push(node)
        if (node.childrenList && node.childrenList.length > 0) {
          traverse(node.childrenList)
        }
      })
    }
    
    traverse(treeData)
    return result
  }
  
  return {
    // 状态
    selectedRecord,
    clipboard,
    canPaste,
    
    // 记录操作
    addNewRecord,
    deleteRecord,
    copyRecord,
    pasteRecord,
    
    // 数据验证和格式化
    validateRecord,
    formatNumericField,
    formatDateField,
    
    // 字段处理
    handleFieldChange,
    getFieldDisplayValue,
    isRequiredField,
    isHighlightField,
    getFieldEditType,
    
    // 树形数据处理
    buildTreeData,
    flattenTreeData
  }
}
