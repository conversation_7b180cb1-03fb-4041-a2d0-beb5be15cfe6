<template>
  <div class="tab-menu">
    <a-tabs
      v-model:activeKey="activeKey"
      type="card"
      @change="handleTabChange"
    >
      <a-tab-pane
        :key="tab.code"
        :tab="tab.value"
        v-for="tab in filteredTabs"
      />
    </a-tabs>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps({
  // 当前层级类型：1-工程项目层级，2-单项工程层级，3-单位工程层级
  levelType: {
    type: Number,
    required: true,
    validator: (value) => [1, 2, 3].includes(value)
  },
  // 当前模块类型：budget-预算, rough-estimate-概算, settlement-结算, material-工料机
  moduleType: {
    type: String,
    required: true,
    validator: (value) => ['budget', 'rough-estimate', 'settlement', 'material'].includes(value)
  },
  // 是否为一级子单项（影响菜单显示）
  isFirstLevelChild: {
    type: Boolean,
    default: true
  },
  // 标准组模式（单位工程简化显示）
  isStandardGroup: {
    type: Boolean,
    default: false
  },
  // 是否有专业工程设置（影响权限控制）
  hasProfessionalProject: {
    type: Boolean,
    default: true
  },
  // 初始激活的tab key
  defaultActiveKey: {
    type: String,
    default: '1'
  }
})

const emit = defineEmits(['tabChange', 'getTitle'])

const activeKey = ref(props.defaultActiveKey)

// 基础菜单配置（从后端获取或静态配置）
const baseMenus = ref({
  1: [ // 工程项目层级
    { code: '1', value: '项目概况' },
    { code: '2', value: '造价分析' },
    { code: '3', value: '取费表' },
    { code: '4', value: '人材机汇总' },
    { code: '5', value: '设备购置费' },
    { code: '6', value: '建设其他费' },
    { code: '7', value: '概算汇总' },
    { code: '8', value: '调整概算' }
  ],
  2: [ // 单项工程层级
    { code: '1', value: '造价分析' },
    { code: '3', value: '取费表' },
    { code: '4', value: '人材机汇总' }
  ],
  3: [ // 单位工程层级
    { code: '1', value: '工程概况' },
    { code: '2', value: '造价分析' },
    { code: '3', value: '取费表' },
    { code: '4', value: '分部分项' },
    { code: '5', value: '措施项目' },
    { code: '6', value: '人材机汇总' },
    { code: '7', value: '其他项目' },
    { code: '8', value: '费用汇总' },
    { code: '9', value: '独立费' }
  ]
})

// 根据模块类型和层级类型过滤菜单
const filteredTabs = computed(() => {
  let tabs = [...(baseMenus.value[props.levelType] || [])]

  // 模块特殊处理逻辑
  tabs = applyModuleSpecialLogic(tabs)

  // 权限控制逻辑
  tabs = applyPermissionLogic(tabs)

  // 标准组模式处理
  if (props.isStandardGroup && props.levelType === 3) {
    const standardGroupItems = ['分部分项', '措施项目', '人材机汇总']
    tabs = tabs.filter(tab => standardGroupItems.includes(tab.value))
  }

  return tabs
})

// 应用模块特殊逻辑
const applyModuleSpecialLogic = (tabs) => {
  const moduleConfig = {
    'budget': {
      // 预算模块 - 功能最全面的标准模式
      exclude: {
        1: ['设备购置费', '建设其他费', '概算汇总', '调整概算'],
        2: ['取费表'],
        3: ['独立费']
      },
      rename: {}
    },
    'rough-estimate': {
      // 概算模块 - 增加概算特有功能
      exclude: {
        1: [],
        2: ['取费表'],
        3: ['措施项目', '其他项目']
      },
      rename: {
        1: { '项目概况': '项目概况' },
        3: { '工程概况': '工程概况' }
      }
    },
    'material': {
      // 工料机模块 - 专注预算书和独立费管理
      exclude: {
        1: ['设备购置费', '建设其他费', '概算汇总', '调整概算'],
        2: [],
        3: ['措施项目', '其他项目']
      },
      rename: {
        3: { '分部分项': '预算书' }
      }
    },
    'settlement': {
      // 结算模块 - 简化菜单，取消取费表，人材机改为调整模式
      exclude: {
        1: ['取费表', '设备购置费', '建设其他费', '概算汇总', '调整概算'],
        2: ['取费表', '人材机汇总'],
        3: props.levelType === 3 ? ['取费表', '造价分析', '独立费'] : ['取费表']
      },
      rename: {
        1: { '人材机汇总': '人材机调整' },
        3: { '人材机汇总': '人材机调整', '工程概况': '项目概况' }
      }
    }
  }

  const config = moduleConfig[props.moduleType]
  if (!config) return tabs

  // 排除不需要的菜单项
  const excludeItems = config.exclude[props.levelType] || []
  tabs = tabs.filter(tab => !excludeItems.includes(tab.value))

  // 重命名菜单项
  const renameMap = config.rename[props.levelType] || {}
  tabs = tabs.map(tab => ({
    ...tab,
    value: renameMap[tab.value] || tab.value
  }))

  return tabs
}

// 应用权限控制逻辑
const applyPermissionLogic = (tabs) => {
  // 非一级子单项：过滤掉人材机汇总和取费表
  if (props.levelType === 2 && !props.isFirstLevelChild) {
    tabs = tabs.filter(tab => !['人材机汇总', '人材机调整', '取费表'].includes(tab.value))
  }

  // 没有专业工程设置时的限制访问
  if (!props.hasProfessionalProject) {
    if (props.levelType === 1) {
      // 工程项目只能查看项目概况
      tabs = tabs.filter(tab => ['项目概况'].includes(tab.value))
    } else if (props.levelType === 3) {
      // 单位工程只能查看分部分项
      tabs = tabs.filter(tab => ['分部分项'].includes(tab.value))
    }
  }

  return tabs
}

// 监听props变化，重新计算过滤后的tabs
watch([
  () => props.levelType,
  () => props.moduleType,
  () => props.isFirstLevelChild,
  () => props.isStandardGroup,
  () => props.hasProfessionalProject
], () => {
  // 当过滤条件变化时，可能需要重置activeKey
  const currentTab = filteredTabs.value.find(tab => tab.code === activeKey.value)
  if (!currentTab && filteredTabs.value.length > 0) {
    activeKey.value = filteredTabs.value[0].code
    handleTabChange(activeKey.value)
  }
}, { immediate: true })

// 处理tab切换
const handleTabChange = (key) => {
  const selectedTab = filteredTabs.value.find(tab => tab.code === key)
  if (selectedTab) {
    activeKey.value = key
    emit('tabChange', {
      code: key,
      value: selectedTab.value,
      levelType: props.levelType,
      moduleType: props.moduleType
    })
    emit('getTitle', selectedTab.value)
  }
}

// 外部调用方法：根据tab名称切换
const changeTabByName = (tabName) => {
  const targetTab = filteredTabs.value.find(tab => tab.value === tabName)
  if (targetTab) {
    handleTabChange(targetTab.code)
    return true
  }
  return false
}

// 外部调用方法：获取当前tab信息
const getCurrentTab = () => {
  return filteredTabs.value.find(tab => tab.code === activeKey.value)
}

// 暴露方法给父组件
defineExpose({
  changeTabByName,
  getCurrentTab,
  filteredTabs
})

// 组件挂载后初始化
onMounted(() => {
  if (filteredTabs.value.length > 0) {
    const initialTab = filteredTabs.value.find(tab => tab.code === activeKey.value) || filteredTabs.value[0]
    handleTabChange(initialTab.code)
  }
})
</script>

<style lang="scss" scoped>
.tab-menu :deep(.ant-tabs) {
  height: 50px;
  position: relative;
  top: 1px;
  box-sizing: border-box;
  font-size: 12px;

  .ant-tabs-nav {
    margin-bottom: 0;
    height: 100%;

    .ant-tabs-tab {
      padding: 8px 20px;
      border: none;
      border-radius: 4px;
      background-color: transparent;
      font-size: 12px;
      border-right: 1px solid #d6d6d6;
      margin-left: 0;
      transition: all 0.2s ease;

      &:last-child {
        border-right: none;
      }

      &:hover {
        background-color: #f0f2f5;
      }
    }

    .ant-tabs-nav-more {
      display: none !important;
    }

    .ant-tabs-tab-active {
      background-color: #deeaff;

      .ant-tabs-tab-btn {
        color: #1890ff;
        font-weight: 500;
      }
    }
  }

  .ant-tabs-content-holder {
    display: none;
  }
}

// 模块特色主题色
.tab-menu {
  &.budget-theme :deep(.ant-tabs-tab-active) {
    background-color: #e6f7ff;
    border-bottom: 2px solid #52c41a;
    .ant-tabs-tab-btn { color: #52c41a; }
  }

  &.rough-estimate-theme :deep(.ant-tabs-tab-active) {
    background-color: #e6f7ff;
    border-bottom: 2px solid #1890ff;
    .ant-tabs-tab-btn { color: #1890ff; }
  }

  &.settlement-theme :deep(.ant-tabs-tab-active) {
    background-color: #f9f0ff;
    border-bottom: 2px solid #722ed1;
    .ant-tabs-tab-btn { color: #722ed1; }
  }

  &.material-theme :deep(.ant-tabs-tab-active) {
    background-color: #fff7e6;
    border-bottom: 2px solid #faad14;
    .ant-tabs-tab-btn { color: #faad14; }
  }
}
</style>