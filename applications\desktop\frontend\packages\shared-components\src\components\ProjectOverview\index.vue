<template>
  <div class="project-overview" :class="`module-${moduleType}`">
    <div class="page-header">
      <h3>{{ pageTitle }}</h3>
      <div class="page-actions">
        <a-button type="primary" @click="handleSave" :loading="saving">
          <template #icon><SaveOutlined /></template>
          保存
        </a-button>
        <a-button @click="handleExport" :loading="exporting">
          <template #icon><ExportOutlined /></template>
          导出
        </a-button>
        <a-button @click="handleLock" v-if="showLockButton">
          <template #icon><LockOutlined v-if="!isLocked" /><UnlockOutlined v-else /></template>
          {{ isLocked ? '解锁' : '锁定' }}
        </a-button>
      </div>
    </div>

    <div class="page-body">
      <a-tabs v-model:activeKey="activeTab" @change="handleTabChange">
        <a-tab-pane key="basic" tab="基本信息">
          <BasicInfo 
            :module-type="moduleType"
            :level-type="levelType"
            :project-data="projectData"
            :table-data="basicInfoData"
            :loading="loading"
            :editable="editable && !isLocked"
            @data-change="handleBasicInfoChange"
            @save="handleBasicInfoSave"
            @delete="handleBasicInfoDelete"
          />
        </a-tab-pane>
        <a-tab-pane key="feature" tab="工程特征" v-if="showEngineerFeature">
          <EngineerFeature
            :module-type="moduleType" 
            :level-type="levelType"
            :project-data="projectData"
            :table-data="featureData"
            :loading="loading"
            :editable="editable && !isLocked"
            @data-change="handleFeatureChange"
            @save="handleFeatureSave"
            @delete="handleFeatureDelete"
          />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, provide } from 'vue'
import { message } from 'ant-design-vue'
import { SaveOutlined, ExportOutlined, LockOutlined, UnlockOutlined } from '@ant-design/icons-vue'
import BasicInfo from './BasicInfo.vue'
import EngineerFeature from './EngineerFeature.vue'
import { useProjectOverview } from './composables/useProjectOverview'
import { getBasicInfoByModule } from './mockData/basicInfoData'
import { getFeatureDataByModule } from './mockData/featureData'
import { getModuleConfig } from './configs/moduleConfigs'

const props = defineProps({
  moduleType: {
    type: String,
    required: true,
    validator: (value) => ['estimate', 'budget', 'settlement', 'review', 'material'].includes(value)
  },
  levelType: {
    type: Number,
    required: true,
    validator: (value) => [1, 2, 3].includes(value)
  },
  projectData: {
    type: Object,
    default: () => ({})
  },
  tabInfo: {
    type: Object,
    default: () => ({})
  },
  editable: {
    type: Boolean,
    default: true
  },
  customConfig: {
    type: Object,
    default: () => ({})
  },
  permissions: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['dataChange', 'save', 'export', 'lock', 'action'])

// 状态管理
const activeTab = ref('basic')
const loading = ref(false)
const saving = ref(false)
const exporting = ref(false)
const isLocked = ref(false)
const basicInfoData = ref([])
const featureData = ref([])

// 业务逻辑
const {
  fetchBasicInfo,
  fetchFeatureInfo,
  saveBasicInfo,
  saveFeatureInfo,
  deleteItem,
  toggleLock,
  exportData
} = useProjectOverview({
  moduleType: props.moduleType,
  levelType: props.levelType
})

// 计算属性
const moduleConfig = computed(() => getModuleConfig(props.moduleType))

const pageTitle = computed(() => {
  const moduleLabel = moduleConfig.value?.name || '项目'
  return `${moduleLabel}概况`
})

const showLockButton = computed(() => {
  return props.permissions.canLock !== false && props.levelType === 3
})

const showEngineerFeature = computed(() => {
  return props.customConfig.hideEngineerFeature !== true
})

// 提供给子组件的上下文
provide('projectOverviewContext', {
  moduleType: props.moduleType,
  levelType: props.levelType,
  moduleConfig,
  permissions: props.permissions,
  customConfig: props.customConfig
})

// 事件处理
const handleTabChange = (key) => {
  activeTab.value = key
  if (key === 'feature' && featureData.value.length === 0) {
    loadFeatureData()
  }
}

const handleSave = async () => {
  try {
    saving.value = true
    
    if (activeTab.value === 'basic') {
      await saveBasicInfo(basicInfoData.value)
    } else if (activeTab.value === 'feature') {
      await saveFeatureInfo(featureData.value)
    }
    
    message.success('保存成功')
    emit('save', {
      tab: activeTab.value,
      data: activeTab.value === 'basic' ? basicInfoData.value : featureData.value
    })
  } catch (error) {
    console.error('保存失败:', error)
    message.error('保存失败')
  } finally {
    saving.value = false
  }
}

const handleExport = async () => {
  try {
    exporting.value = true
    
    const exportResult = await exportData({
      basic: basicInfoData.value,
      feature: featureData.value,
      project: props.projectData
    })
    
    message.success('导出成功')
    emit('export', exportResult)
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const handleLock = async () => {
  try {
    const newLockStatus = !isLocked.value
    await toggleLock(newLockStatus)
    
    isLocked.value = newLockStatus
    message.success(newLockStatus ? '锁定成功' : '解锁成功')
    emit('lock', newLockStatus)
  } catch (error) {
    console.error('锁定操作失败:', error)
    message.error('操作失败')
  }
}

// 子组件事件处理
const handleBasicInfoChange = (data) => {
  basicInfoData.value = data
  emit('dataChange', { type: 'basic', data })
}

const handleBasicInfoSave = async (data) => {
  try {
    await saveBasicInfo(data)
    basicInfoData.value = data
    message.success('基本信息保存成功')
  } catch (error) {
    message.error('保存失败')
  }
}

const handleBasicInfoDelete = async (sequenceNbr) => {
  try {
    await deleteItem(sequenceNbr)
    await loadBasicData()
    message.success('删除成功')
  } catch (error) {
    message.error('删除失败')
  }
}

const handleFeatureChange = (data) => {
  featureData.value = data
  emit('dataChange', { type: 'feature', data })
}

const handleFeatureSave = async (data) => {
  try {
    await saveFeatureInfo(data)
    featureData.value = data
    message.success('工程特征保存成功')
  } catch (error) {
    message.error('保存失败')
  }
}

const handleFeatureDelete = async (sequenceNbr) => {
  try {
    await deleteItem(sequenceNbr)
    await loadFeatureData()
    message.success('删除成功')
  } catch (error) {
    message.error('删除失败')
  }
}

// 数据加载
const loadBasicData = async () => {
  try {
    loading.value = true

    // 如果有传入的数据，使用传入的数据，否则使用模拟数据
    if (props.projectData && Object.keys(props.projectData).length > 0) {
      const result = await fetchBasicInfo()
      basicInfoData.value = result || []
    } else {
      // 使用模拟数据
      const mockData = getBasicInfoByModule(props.moduleType)
      basicInfoData.value = mockData
    }
  } catch (error) {
    console.error('加载基本信息失败:', error)
    // 出错时也使用模拟数据
    const mockData = getBasicInfoByModule(props.moduleType)
    basicInfoData.value = mockData
  } finally {
    loading.value = false
  }
}

const loadFeatureData = async () => {
  try {
    loading.value = true

    // 如果有传入的数据，使用传入的数据，否则使用模拟数据
    if (props.projectData && Object.keys(props.projectData).length > 0) {
      const result = await fetchFeatureInfo()
      featureData.value = result || []
    } else {
      // 使用模拟数据
      const mockData = getFeatureDataByModule(props.moduleType)
      featureData.value = mockData
    }
  } catch (error) {
    console.error('加载工程特征失败:', error)
    // 出错时也使用模拟数据
    const mockData = getFeatureDataByModule(props.moduleType)
    featureData.value = mockData
  } finally {
    loading.value = false
  }
}

// 监听器
watch(() => props.projectData, () => {
  if (activeTab.value === 'basic') {
    loadBasicData()
  }
}, { deep: true })

watch(() => [props.moduleType, props.levelType], () => {
  loadBasicData()
  if (activeTab.value === 'feature') {
    loadFeatureData()
  }
})

// 生命周期
onMounted(() => {
  loadBasicData()
})
</script>

<style lang="scss" scoped>
.project-overview {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;

    h3 {
      margin: 0;
      color: #333;
      font-weight: 600;
      font-size: 16px;
    }

    .page-actions {
      display: flex;
      gap: 8px;
    }
  }

  .page-body {
    flex: 1;
    overflow: hidden;
    padding: 16px 24px;

    :deep(.ant-tabs) {
      height: 100%;
      display: flex;
      flex-direction: column;

      .ant-tabs-nav {
        margin-bottom: 16px;

        .ant-tabs-tab {
          padding: 8px 16px;

          &.ant-tabs-tab-active {
            .ant-tabs-tab-btn {
              color: var(--module-primary-color, #1890ff);
              font-weight: 500;
            }
          }
        }

        .ant-tabs-ink-bar {
          background: var(--module-primary-color, #1890ff);
        }
      }

      .ant-tabs-content-holder {
        flex: 1;
        overflow: hidden;

        .ant-tabs-tabpane {
          height: 100%;
        }
      }
    }
  }
  // 模块主题色变量
  &.module-budget {
    --module-primary-color: #52c41a;
  }

  &.module-settlement {
    --module-primary-color: #fa8c16;
  }

  &.module-review {
    --module-primary-color: #722ed1;
  }

  &.module-estimate {
    --module-primary-color: #1890ff;
  }

  &.module-material {
    --module-primary-color: #13c2c2;
  }
}

// 全局样式，参考原有项目
:deep(.table-edit-common) {
  .title-bold {
    font-weight: bold;
    background-color: #f5f5f5;
  }

  .color-red {
    color: #de3f3f;
    font-weight: 500;
  }

  .row-lock-color {
    background-color: #bfbfbf;

    .vxe-cell {
      color: #666;
    }
  }

  .vxe-cell .vxe-cell--label {
    user-select: none;
  }
}

// 表格无外边框样式
:deep(.table-no-outer-border) {
  border: none;

  .vxe-table--border-line {
    border: none;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .project-overview {
    .page-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      padding: 12px 16px;

      .page-actions {
        width: 100%;
        justify-content: flex-end;
      }
    }

    .page-body {
      padding: 12px 16px;
    }
  }
}
</style>
