<!--
  CostTable 模块特有组件容器
  根据模块类型动态加载和显示特有组件
-->
<template>
  <div class="cost-table-modules">
    <!-- 概算模块特有组件 -->
    <template v-if="moduleType === 'gaiSuan'">
      <!-- 引入工程量组件 -->
      <component
        v-if="activeComponent === 'introductionQuantity'"
        :is="IntroductionQuantity"
        v-model:visible="componentVisible"
        :record="currentRecord"
        @confirm="handleIntroductionQuantity"
        @cancel="closeComponent"
      />
      
      <!-- 批量修改主材组件 -->
      <component
        v-if="activeComponent === 'batchModifyMainMaterials'"
        :is="BatchModifyMainMaterials"
        v-model:visible="componentVisible"
        :records="selectedRecords"
        @confirm="handleBatchModifyMainMaterials"
        @cancel="closeComponent"
      />
      
      <!-- 整理分部组件 -->
      <component
        v-if="activeComponent === 'organizeFb'"
        :is="OrganizeFb"
        v-model:visible="componentVisible"
        :data="tableData"
        @confirm="handleOrganizeFb"
        @cancel="closeComponent"
      />
      
      <!-- 整理子目组件 -->
      <component
        v-if="activeComponent === 'organizeZm'"
        :is="OrganizeZm"
        v-model:visible="componentVisible"
        :data="tableData"
        @confirm="handleOrganizeZm"
        @cancel="closeComponent"
      />
    </template>

    <!-- 预算/结算/审核模块特有组件 -->
    <template v-if="['yuSuan', 'jieSuan', 'shenHe'].includes(moduleType)">
      <!-- 编码重置组件 -->
      <component
        v-if="activeComponent === 'codeReset'"
        :is="CodeReset"
        v-model:visible="componentVisible"
        :records="selectedRecords"
        @confirm="handleCodeReset"
        @cancel="closeComponent"
      />
      
      <!-- 定额行政措施组件 -->
      <component
        v-if="activeComponent === 'deHangZCSB'"
        :is="DEHangZCSB"
        v-model:visible="componentVisible"
        :record="currentRecord"
        @confirm="handleDEHangZCSB"
        @cancel="closeComponent"
      />
      
      <!-- 文本编辑弹窗 -->
      <component
        v-if="activeComponent === 'editTextarea'"
        :is="EditTextareaModal"
        v-model:visible="componentVisible"
        :record="currentRecord"
        :field="editField"
        @confirm="handleEditTextarea"
        @cancel="closeComponent"
      />
      
      <!-- 同步名称到定额组件 -->
      <component
        v-if="activeComponent === 'syncNameToDE'"
        :is="SyncNameToDE"
        v-model:visible="componentVisible"
        :records="selectedRecords"
        @confirm="handleSyncNameToDE"
        @cancel="closeComponent"
      />
    </template>

    <!-- 审核模块特有组件 -->
    <template v-if="moduleType === 'shenHe'">
      <!-- 审核对比组件 -->
      <component
        v-if="activeComponent === 'auditComparison'"
        :is="AuditComparison"
        v-model:visible="componentVisible"
        :record="currentRecord"
        @confirm="handleAuditComparison"
        @cancel="closeComponent"
      />
      
      <!-- 审核意见组件 -->
      <component
        v-if="activeComponent === 'auditOpinion'"
        :is="AuditOpinion"
        v-model:visible="componentVisible"
        :record="currentRecord"
        @confirm="handleAuditOpinion"
        @cancel="closeComponent"
      />
    </template>

    <!-- 结算模块特有组件 -->
    <template v-if="moduleType === 'jieSuan'">
      <!-- 关联合同组件 -->
      <component
        v-if="activeComponent === 'contractAssociation'"
        :is="ContractAssociation"
        v-model:visible="componentVisible"
        :record="currentRecord"
        @confirm="handleContractAssociation"
        @cancel="closeComponent"
      />
      
      <!-- 进度结算组件 -->
      <component
        v-if="activeComponent === 'progressSettlement'"
        :is="ProgressSettlement"
        v-model:visible="componentVisible"
        :records="selectedRecords"
        @confirm="handleProgressSettlement"
        @cancel="closeComponent"
      />
    </template>

    <!-- 工料机模块特有组件 -->
    <template v-if="moduleType === 'gongLiaoJi'">
      <!-- 设置标准类型组件 -->
      <component
        v-if="activeComponent === 'setStandardType'"
        :is="SetStandardType"
        v-model:visible="componentVisible"
        :record="currentRecord"
        @confirm="handleSetStandardType"
        @cancel="closeComponent"
      />
      
      <!-- 设置主材组件 -->
      <component
        v-if="activeComponent === 'setMainMaterial'"
        :is="SetMainMaterial"
        v-model:visible="componentVisible"
        :record="currentRecord"
        @confirm="handleSetMainMaterial"
        @cancel="closeComponent"
      />
      
      <!-- 项目属性关联组件 -->
      <component
        v-if="activeComponent === 'projectAttrAssociation'"
        :is="ProjectAttrAssociation"
        v-model:visible="componentVisible"
        :record="currentRecord"
        @confirm="handleProjectAttrAssociation"
        @cancel="closeComponent"
      />
    </template>

    <!-- 通用组件 -->
    <!-- 补充清单组件 -->
    <component
      v-if="activeComponent === 'bcQd'"
      :is="BcQd"
      v-model:visible="componentVisible"
      :record="currentRecord"
      @confirm="handleBcQd"
      @cancel="closeComponent"
    />
    
    <!-- 补充定额组件 -->
    <component
      v-if="activeComponent === 'bcDe'"
      :is="BcDe"
      v-model:visible="componentVisible"
      :record="currentRecord"
      @confirm="handleBcDe"
      @cancel="closeComponent"
    />
    
    <!-- 补充人材机组件 -->
    <component
      v-if="activeComponent === 'bcRcj'"
      :is="BcRcj"
      v-model:visible="componentVisible"
      :record="currentRecord"
      @confirm="handleBcRcj"
      @cancel="closeComponent"
    />
    
    <!-- Excel导入组件 -->
    <component
      v-if="activeComponent === 'importExcel'"
      :is="ImportExcel"
      v-model:visible="componentVisible"
      :module-type="moduleType"
      @confirm="handleImportExcel"
      @cancel="closeComponent"
    />
    
    <!-- 价格模型组件 -->
    <component
      v-if="activeComponent === 'priceModel'"
      :is="PriceModel"
      v-model:visible="componentVisible"
      :record="currentRecord"
      @confirm="handlePriceModel"
      @cancel="closeComponent"
    />
  </div>
</template>

<script setup>
import { ref, defineAsyncComponent } from 'vue'

// Props
const props = defineProps({
  moduleType: {
    type: String,
    required: true
  },
  currentRecord: {
    type: Object,
    default: () => ({})
  },
  selectedRecords: {
    type: Array,
    default: () => []
  },
  tableData: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits([
  'componentAction',
  'dataChange',
  'recordUpdate'
])

// 响应式数据
const activeComponent = ref('')
const componentVisible = ref(false)
const editField = ref('')

// 异步加载组件
// 概算模块组件
const IntroductionQuantity = defineAsyncComponent(() => 
  import('../modules/estimate/IntroductionQuantity.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)
const BatchModifyMainMaterials = defineAsyncComponent(() => 
  import('../modules/estimate/BatchModifyMainMaterials.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)
const OrganizeFb = defineAsyncComponent(() => 
  import('../modules/estimate/OrganizeFb.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)
const OrganizeZm = defineAsyncComponent(() => 
  import('../modules/estimate/OrganizeZm.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)

// 预算/结算/审核模块组件
const CodeReset = defineAsyncComponent(() => 
  import('../modules/budget/CodeReset.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)
const DEHangZCSB = defineAsyncComponent(() => 
  import('../modules/budget/DEHangZCSB.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)
const EditTextareaModal = defineAsyncComponent(() => 
  import('../modules/budget/EditTextareaModal.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)
const SyncNameToDE = defineAsyncComponent(() => 
  import('../modules/budget/SyncNameToDE.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)

// 审核模块组件
const AuditComparison = defineAsyncComponent(() => 
  import('../modules/review/AuditComparison.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)
const AuditOpinion = defineAsyncComponent(() => 
  import('../modules/review/AuditOpinion.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)

// 结算模块组件
const ContractAssociation = defineAsyncComponent(() => 
  import('../modules/settlement/ContractAssociation.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)
const ProgressSettlement = defineAsyncComponent(() => 
  import('../modules/settlement/ProgressSettlement.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)

// 工料机模块组件
const SetStandardType = defineAsyncComponent(() => 
  import('../modules/material/SetStandardType.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)
const SetMainMaterial = defineAsyncComponent(() => 
  import('../modules/material/SetMainMaterial.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)
const ProjectAttrAssociation = defineAsyncComponent(() => 
  import('../modules/material/ProjectAttrAssociation.vue').catch(() => 
    import('../common/PlaceholderComponent.vue')
  )
)

// 通用组件
const BcQd = defineAsyncComponent(() => import('../common/BcQd.vue'))
const BcDe = defineAsyncComponent(() => import('../common/BcDe.vue'))
const BcRcj = defineAsyncComponent(() => import('../common/BcRcj.vue'))
const ImportExcel = defineAsyncComponent(() => import('../common/ImportExcel.vue'))
const PriceModel = defineAsyncComponent(() => import('../common/PriceModel.vue'))

// 方法
const openComponent = (componentName, field = '') => {
  activeComponent.value = componentName
  editField.value = field
  componentVisible.value = true
}

const closeComponent = () => {
  activeComponent.value = ''
  componentVisible.value = false
  editField.value = ''
}

// 各种组件的处理方法
const handleIntroductionQuantity = (data) => {
  emit('componentAction', { type: 'introductionQuantity', data })
  closeComponent()
}

const handleBatchModifyMainMaterials = (data) => {
  emit('componentAction', { type: 'batchModifyMainMaterials', data })
  closeComponent()
}

const handleOrganizeFb = (data) => {
  emit('componentAction', { type: 'organizeFb', data })
  closeComponent()
}

const handleOrganizeZm = (data) => {
  emit('componentAction', { type: 'organizeZm', data })
  closeComponent()
}

const handleCodeReset = (data) => {
  emit('componentAction', { type: 'codeReset', data })
  closeComponent()
}

const handleDEHangZCSB = (data) => {
  emit('componentAction', { type: 'deHangZCSB', data })
  closeComponent()
}

const handleEditTextarea = (data) => {
  emit('componentAction', { type: 'editTextarea', data, field: editField.value })
  closeComponent()
}

const handleSyncNameToDE = (data) => {
  emit('componentAction', { type: 'syncNameToDE', data })
  closeComponent()
}

const handleAuditComparison = (data) => {
  emit('componentAction', { type: 'auditComparison', data })
  closeComponent()
}

const handleAuditOpinion = (data) => {
  emit('componentAction', { type: 'auditOpinion', data })
  closeComponent()
}

const handleContractAssociation = (data) => {
  emit('componentAction', { type: 'contractAssociation', data })
  closeComponent()
}

const handleProgressSettlement = (data) => {
  emit('componentAction', { type: 'progressSettlement', data })
  closeComponent()
}

const handleSetStandardType = (data) => {
  emit('componentAction', { type: 'setStandardType', data })
  closeComponent()
}

const handleSetMainMaterial = (data) => {
  emit('componentAction', { type: 'setMainMaterial', data })
  closeComponent()
}

const handleProjectAttrAssociation = (data) => {
  emit('componentAction', { type: 'projectAttrAssociation', data })
  closeComponent()
}

const handleBcQd = (data) => {
  emit('componentAction', { type: 'bcQd', data })
  closeComponent()
}

const handleBcDe = (data) => {
  emit('componentAction', { type: 'bcDe', data })
  closeComponent()
}

const handleBcRcj = (data) => {
  emit('componentAction', { type: 'bcRcj', data })
  closeComponent()
}

const handleImportExcel = (data) => {
  emit('componentAction', { type: 'importExcel', data })
  closeComponent()
}

const handlePriceModel = (data) => {
  emit('componentAction', { type: 'priceModel', data })
  closeComponent()
}

// 暴露方法
defineExpose({
  openComponent,
  closeComponent
})
</script>

<style scoped>
.cost-table-modules {
  position: relative;
}
</style>
